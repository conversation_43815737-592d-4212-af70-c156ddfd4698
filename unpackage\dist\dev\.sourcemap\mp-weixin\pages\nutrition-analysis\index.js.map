{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/nutrition-analysis/index.vue?fea1", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/nutrition-analysis/index.vue?fe59", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/nutrition-analysis/index.vue?bbaa", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/nutrition-analysis/index.vue?1138", "uni-app:///pages/nutrition-analysis/index.vue", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/nutrition-analysis/index.vue?d22f", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/nutrition-analysis/index.vue?bc77"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "qiunData<PERSON><PERSON><PERSON>", "data", "selectedDate", "datePickerValue", "startDate", "endDate", "formattedDate", "calorieColor", "nutrientLegendItems", "label", "color", "lastDataFetchTime", "dataCacheTime", "showCalorie<PERSON>hart", "showNutrientChart", "calorieChartOpts", "padding", "enableScroll", "dataLabel", "legend", "show", "xAxis", "disable<PERSON><PERSON>", "yAxis", "gridType", "<PERSON><PERSON><PERSON><PERSON>", "extra", "line", "type", "width", "nutrientChartOpts", "calorieChartData", "categories", "series", "nutrientChartData", "computed", "nutritionData", "nutritionTrend", "nutritionAdvice", "isLoading", "dataChanged", "mainNutritionItems", "value", "percentage", "dateTitle", "today", "selectedDateObj", "watch", "handler", "deep", "setTimeout", "onLoad", "onReady", "onPullDownRefresh", "console", "uni", "onUnload", "onShow", "then", "methods", "fetchNutritionData", "fetchNutritionTrend", "fetchNutritionAdvice", "formatPercentage", "formatChartDate", "fetchAllNutritionData", "dateStr", "Promise", "date", "title", "icon", "updateChartData", "name", "changeDate", "newDate", "duration", "onDateChange", "getAdviceIcon", "warning", "info", "danger", "success", "getDateMonthsAgo", "refresh<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qXAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACoItnB;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAOA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC,sBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACAC;MAEA;MACAC;QACAL;QACAM;QACAC;QACAC;QACAC;UACAC;QACA;QACAC;UACAC;QACA;QACAC;UACAC;UACAC;UACAxB;QACA;QACAyB;UACAC;YACAC;YACAC;UACA;QACA;MACA;MACAC;QACApB;QACAM;QACAC;QACAC;QACAC;UACAC;QACA;QACAC;UACAC;QACA;QACAC;UACAC;UACAC;UACAxB;QACA;QACAyB;UACAC;YACAC;YACAC;UACA;QACA;MACA;MACA;MACAE;QACAC;QACAC;MACA;MACAC;QACAF;QACAC;MACA;IACA;EACA;EACAE,0CACA;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;IAEA;IACAC;MACA,QACA;QACAhC;QACAiC;QACAC;QACAjC;MACA,GACA;QACAD;QACAiC;QACAC;QACAjC;MACA,GACA;QACAD;QACAiC;QACAC;QACAjC;MACA,GACA;QACAD;QACAiC;QACAC;QACAjC;MACA,EACA;IACA;IAEA;IACAkC;MACA;MACA;MACAC;MAEA;MACAC;MAEA;MAEA;MACA;MACA;MAEA;IACA;IAEA;IACAxC;MACA;IACA;EAAA,EACA;EACAyC;IACA;IACAV;MACAW;QACA;UACA;QACA;MACA;MACAC;IACA;IACA;IACApC;MAAA;MACA;QACA;QACAqC;UACA;QACA;MACA;IACA;IACApC;MAAA;MACA;QACA;QACAoC;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;;IAEA;EAAA,CACA;EACAC;IACA;IACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;EAAA,CACA;EACAC;IAAA;IACA;IACA;;IAEA;IACA;MACA;MACAH;MACA,6BACAI;QACA;QACA;;QAEA;QACA;UACA;QACA;MACA;IACA;MACA;MACAJ;MACA;IACA;EACA;EACAK,yCACA;IACAC;IACAC;IACAC;EACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA;gBAAA,OACAC,aACA;kBAAAC;gBAAA,IACA;kBAAAxC;gBAAA,IACA;kBAAAwC;gBAAA,GACA;cAAA;gBAEA;gBACA;;gBAEA;gBACA;gBAAA,iCAEAD;cAAA;gBAAA;gBAAA;gBAEAb;gBACAC;kBACAc;kBACAC;gBACA;gBAAA,iCACAH;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAI;MAAA;MACA;QACA;MACA;;MAEA;MACA;QACAvC;QACAC,SACA;UACAuC;UACAvE;QACA;MAEA;;MAEA;MACA;QACA+B;QACAC,SACA;UACAuC;UACAvE;QACA,GACA;UACAuE;UACAvE;QACA,GACA;UACAuE;UACAvE;QACA;MAEA;;MAEA;MACA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAwE;MACA;MACAC;;MAEA;MACA;MACA7B;;MAEA;MACA6B;MAEA;QACA;QACAnB;UACAc;UACAC;UACAK;QACA;QACA;MACA;MAEA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACAC;QACAC;QACAC;QACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAd;MACA;IACA;IAEA;IACAe;MACA;MACA;;MAEA;MACA;MACA;;MAEA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;AC/gBA;AAAA;AAAA;AAAA;AAA6oC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAjqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/nutrition-analysis/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/nutrition-analysis/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=040da7a6&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/nutrition-analysis/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=040da7a6&\"", "var components\ntry {\n  components = {\n    qiunDataCharts: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts\" */ \"@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = !_vm.isLoading\n    ? _vm.__map(_vm.mainNutritionItems, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.formatPercentage(item.percentage)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var l1 = !_vm.isLoading\n    ? _vm.__map(_vm.nutritionAdvice, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m1 = _vm.getAdviceIcon(item.type || \"info\")\n        return {\n          $orig: $orig,\n          m1: m1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"nutrition-analysis\">\r\n    <!-- 顶部安全区域 -->\r\n    <view class=\"safe-area\"></view>\r\n\r\n    <!-- 顶部标题 -->\r\n    <view class=\"header\">\r\n      <text class=\"title\">营养分析</text>\r\n    </view>\r\n\r\n    <!-- 内联式日期选择器（替换原有的弹出式日历） -->\r\n    <view class=\"date-selector\">\r\n      <view class=\"date-actions\">\r\n        <view class=\"date-arrow\" @click=\"changeDate(-1)\">\r\n          <image src=\"/static/icons/arrow-left.png\"></image>\r\n        </view>\r\n        <picker mode=\"date\" :value=\"datePickerValue\" :end=\"endDate\" @change=\"onDateChange\">\r\n          <view class=\"date-display\">\r\n            <text class=\"date-title\">{{ dateTitle }}</text>\r\n            <text class=\"date-value\">{{ formattedDate }}</text>\r\n          </view>\r\n        </picker>\r\n        <view class=\"date-arrow\" @click=\"changeDate(1)\">\r\n          <image src=\"/static/icons/arrow-right.png\"></image>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 加载中 -->\r\n    <view v-if=\"isLoading\" class=\"loading-container\">\r\n      <view class=\"loading-box\">\r\n        <view class=\"loading-spinner\"></view>\r\n        <text class=\"loading-text\">加载中...</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <view v-else class=\"content-container\">\r\n      <!-- 核心营养素 -->\r\n      <view class=\"core-nutrients card\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">核心营养素</text>\r\n          <text class=\"date-info\">{{ formattedDate }}</text>\r\n        </view>\r\n\r\n        <view class=\"nutrients-grid\">\r\n          <view class=\"nutrient-card\" v-for=\"(item, index) in mainNutritionItems\" :key=\"index\">\r\n            <view class=\"nutrient-header\">\r\n              <text class=\"nutrient-label\">{{ item.label }}</text>\r\n              <text class=\"nutrient-value\">{{ item.value }}</text>\r\n            </view>\r\n            <view class=\"progress-container\">\r\n              <view class=\"progress-bar\">\r\n                <view class=\"progress\" :style=\"{ width: item.percentage + '%', backgroundColor: item.color }\"></view>\r\n              </view>\r\n              <text class=\"percentage-text\">{{ formatPercentage(item.percentage) }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 卡路里周趋势 -->\r\n      <view class=\"trend-chart card\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">卡路里周趋势</text>\r\n          <view class=\"legend\">\r\n            <view class=\"legend-item\">\r\n              <view class=\"legend-color\" :style=\"{ backgroundColor: calorieColor }\"></view>\r\n              <text class=\"legend-label\">热量</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"chart-container\">\r\n          <qiun-data-charts\r\n            v-if=\"showCalorieChart\"\r\n            type=\"line\"\r\n            :opts=\"calorieChartOpts\"\r\n            :chartData=\"calorieChartData\"\r\n            canvasId=\"calorieChart\"\r\n            ref=\"calorieChart\"\r\n          />\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 营养素周趋势 -->\r\n      <view class=\"trend-chart card\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">营养素周趋势</text>\r\n          <view class=\"legend\">\r\n            <view class=\"legend-item\" v-for=\"(item, index) in nutrientLegendItems\" :key=\"index\">\r\n              <view class=\"legend-color\" :style=\"{ backgroundColor: item.color }\"></view>\r\n              <text class=\"legend-label\">{{ item.label }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"chart-container\">\r\n          <qiun-data-charts\r\n            v-if=\"showNutrientChart\"\r\n            type=\"line\"\r\n            :opts=\"nutrientChartOpts\"\r\n            :chartData=\"nutrientChartData\"\r\n            canvasId=\"nutrientChart\"\r\n            ref=\"nutrientChart\"\r\n          />\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 营养建议 -->\r\n      <view class=\"nutrition-advice card\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">营养建议</text>\r\n        </view>\r\n\r\n        <view class=\"advice-content\">\r\n          <view class=\"advice-item\" v-for=\"(item, index) in nutritionAdvice\" :key=\"index\">\r\n            <view class=\"advice-icon\" :class=\"item.type || 'info'\">\r\n              <image :src=\"getAdviceIcon(item.type || 'info')\"></image>\r\n            </view>\r\n            <view class=\"advice-text\">\r\n              <text class=\"advice-title\">{{ item.title }}</text>\r\n              <text class=\"advice-desc\">{{ item.description }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { formatDate } from '@/utils/date.js'\r\nimport { mapActions, mapGetters } from 'vuex'\r\n// 移除原uCharts导入\r\n// import uCharts from '@/components/u-charts/u-charts.js'\r\n// 导入秋云ucharts组件\r\n// qiun-data-charts是一个高性能的图表组件，支持各端平台，包括H5、小程序、APP等\r\nimport qiunDataCharts from \"uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue\"\r\n\r\nexport default {\r\n  components: {\r\n    qiunDataCharts\r\n  },\r\n  data() {\r\n    return {\r\n      selectedDate: new Date(),\r\n      datePickerValue: formatDate(new Date(), 'yyyy-MM-dd'), // 添加日期选择器变量\r\n      startDate: this.getDateMonthsAgo(3), // 三个月前\r\n      endDate: new Date().toISOString().split('T')[0], // 今天\r\n      formattedDate: '',\r\n      calorieColor: '#FF9800',\r\n      nutrientLegendItems: [\r\n        { label: '蛋白质', color: '#4CAF50' },\r\n        { label: '碳水', color: '#2196F3' },\r\n        { label: '脂肪', color: '#E91E63' }\r\n      ],\r\n      // 数据缓存时间戳，用于判断是否需要重新获取数据\r\n      lastDataFetchTime: 0,\r\n      // 缓存刷新间隔，单位为毫秒（默认5分钟）\r\n      dataCacheTime: 5 * 60 * 1000,\r\n      // 控制图表显示的变量\r\n      showCalorieChart: true,\r\n      showNutrientChart: true,\r\n\r\n      // 添加图表配置选项\r\n      calorieChartOpts: {\r\n        color: ['#FF9800'],\r\n        padding: [30, 40, 20, 40],\r\n        enableScroll: false,\r\n        dataLabel: false,\r\n        legend: {\r\n          show: true\r\n        },\r\n        xAxis: {\r\n          disableGrid: true\r\n        },\r\n        yAxis: {\r\n          gridType: 'dash',\r\n          dashLength: 2,\r\n          data: []\r\n        },\r\n        extra: {\r\n          line: {\r\n            type: 'curve',\r\n            width: 2\r\n          }\r\n        }\r\n      },\r\n      nutrientChartOpts: {\r\n        color: ['#4CAF50', '#2196F3', '#E91E63'],\r\n        padding: [30, 40, 20, 40],\r\n        enableScroll: false,\r\n        dataLabel: false,\r\n        legend: {\r\n          show: true\r\n        },\r\n        xAxis: {\r\n          disableGrid: true\r\n        },\r\n        yAxis: {\r\n          gridType: 'dash',\r\n          dashLength: 2,\r\n          data: []\r\n        },\r\n        extra: {\r\n          line: {\r\n            type: 'curve',\r\n            width: 2\r\n          }\r\n        }\r\n      },\r\n      // 图表数据\r\n      calorieChartData: {\r\n        categories: [],\r\n        series: []\r\n      },\r\n      nutrientChartData: {\r\n        categories: [],\r\n        series: []\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters({\r\n      nutritionData: 'nutrition/nutritionData',\r\n      nutritionTrend: 'nutrition/nutritionTrend',\r\n      nutritionAdvice: 'nutrition/nutritionAdvice',\r\n      isLoading: 'nutrition/isLoading',\r\n      dataChanged: 'nutrition/dataChanged' // 添加数据变更标志\r\n    }),\r\n\r\n    // 格式化主要营养素数据\r\n    mainNutritionItems() {\r\n      return [\r\n        {\r\n          label: '热量',\r\n          value: this.nutritionData.calorie + 'kcal',\r\n          percentage: this.nutritionData.caloriePercentage || 0,\r\n          color: '#FF9800'\r\n        },\r\n        {\r\n          label: '蛋白质',\r\n          value: this.nutritionData.protein + 'g',\r\n          percentage: this.nutritionData.proteinPercentage || 0,\r\n          color: '#4CAF50'\r\n        },\r\n        {\r\n          label: '碳水',\r\n          value: this.nutritionData.carbs + 'g',\r\n          percentage: this.nutritionData.carbsPercentage || 0,\r\n          color: '#2196F3'\r\n        },\r\n        {\r\n          label: '脂肪',\r\n          value: this.nutritionData.fat + 'g',\r\n          percentage: this.nutritionData.fatPercentage || 0,\r\n          color: '#E91E63'\r\n        }\r\n      ]\r\n    },\r\n\r\n    // 日期标题计算属性\r\n    dateTitle() {\r\n      // 判断是否是今天、昨天或前天\r\n      const today = new Date()\r\n      today.setHours(0, 0, 0, 0)\r\n\r\n      const selectedDateObj = new Date(this.selectedDate)\r\n      selectedDateObj.setHours(0, 0, 0, 0)\r\n\r\n      const diffDays = Math.round((selectedDateObj - today) / (1000 * 60 * 60 * 24))\r\n\r\n      if (diffDays === 0) return '今天'\r\n      if (diffDays === -1) return '昨天'\r\n      if (diffDays === -2) return '前天'\r\n\r\n      return '' // 其他日期不显示特殊标题\r\n    },\r\n\r\n    // 格式化日期显示\r\n    formattedDate() {\r\n      return formatDate(this.selectedDate, 'yyyy年MM月dd日')\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听营养趋势数据，更新图表\r\n    nutritionTrend: {\r\n      handler(newValue) {\r\n        if (newValue && newValue.dateList && newValue.dateList.length > 0) {\r\n          this.updateChartData()\r\n        }\r\n      },\r\n      deep: true\r\n    },\r\n    // 监听图表显示变量，在图表隐藏后短暂延迟重新显示\r\n    showCalorieChart(val) {\r\n      if (val === false) {\r\n        // 在下一个宏任务中重新显示图表，触发组件重新创建\r\n        setTimeout(() => {\r\n          this.showCalorieChart = true\r\n        }, 50)\r\n      }\r\n    },\r\n    showNutrientChart(val) {\r\n      if (val === false) {\r\n        // 在下一个宏任务中重新显示图表，触发组件重新创建\r\n        setTimeout(() => {\r\n          this.showNutrientChart = true\r\n        }, 50)\r\n      }\r\n    }\r\n  },\r\n  onLoad() {\r\n    // 初始化日期选择器的值\r\n    this.datePickerValue = formatDate(this.selectedDate, 'yyyy-MM-dd')\r\n    this.formattedDate = formatDate(this.selectedDate, 'yyyy年MM月dd日')\r\n    this.fetchAllNutritionData()\r\n  },\r\n  onReady() {\r\n    // 不再需要手动初始化图表\r\n    // setTimeout(() => {\r\n    //   this.initCharts()\r\n    // }, 300)\r\n\r\n    // 不再需要监听日历状态\r\n  },\r\n  onPullDownRefresh() {\r\n    // 下拉刷新时，强制获取新数据，忽略缓存\r\n    console.log('营养分析：下拉刷新，强制获取新数据')\r\n    this.fetchAllNutritionData().then(() => {\r\n      uni.stopPullDownRefresh()\r\n    })\r\n  },\r\n  onUnload() {\r\n    // 不再需要清除定时器\r\n  },\r\n  onShow() {\r\n    const currentTime = Date.now()\r\n    const timeSinceLastFetch = currentTime - this.lastDataFetchTime\r\n\r\n    // 检查是否需要刷新数据：首次加载、缓存过期或数据已变更\r\n    if (this.lastDataFetchTime === 0 || timeSinceLastFetch > this.dataCacheTime || this.dataChanged) {\r\n      // 如果是首次加载、缓存已过期或数据已变更，则重新获取数据\r\n      console.log('营养分析：刷新数据', this.dataChanged ? '（数据已变更）' : '（缓存过期）')\r\n      this.fetchAllNutritionData()\r\n        .then(() => {\r\n          // 更新数据缓存时间戳\r\n          this.lastDataFetchTime = Date.now()\r\n\r\n          // 重置数据变更标志\r\n          if (this.dataChanged) {\r\n            this.$store.dispatch('nutrition/setDataChanged', false)\r\n          }\r\n        })\r\n    } else {\r\n      // 缓存有效，只刷新图表渲染\r\n      console.log('营养分析：使用缓存数据，仅刷新图表')\r\n      this.refreshCharts()\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions({\r\n      fetchNutritionData: 'nutrition/fetchNutritionData',\r\n      fetchNutritionTrend: 'nutrition/fetchNutritionTrend',\r\n      fetchNutritionAdvice: 'nutrition/fetchNutritionAdvice'\r\n    }),\r\n\r\n    // 格式化百分比，保留整数\r\n    formatPercentage(value) {\r\n      if (!value && value !== 0) return '0%'\r\n      return Math.round(value) + '%'\r\n    },\r\n\r\n    // 格式化趋势图X轴日期显示，只保留日\r\n    formatChartDate(dateStr) {\r\n      if (!dateStr) return '';\r\n      // 将日期如 2025-04-17 格式化为 17\r\n      const parts = dateStr.split('-');\r\n      if (parts.length >= 3) {\r\n        return parts[2];\r\n      }\r\n      return dateStr;\r\n    },\r\n\r\n    // 获取所有营养相关数据\r\n    async fetchAllNutritionData() {\r\n      try {\r\n        const dateStr = formatDate(this.selectedDate, 'yyyy-MM-dd')\r\n        await Promise.all([\r\n          this.fetchNutritionData({ date: dateStr }),\r\n          this.fetchNutritionTrend({ type: 'week' }),\r\n          this.fetchNutritionAdvice({ date: dateStr })\r\n        ])\r\n\r\n        // 更新数据缓存时间戳\r\n        this.lastDataFetchTime = Date.now()\r\n\r\n        // 更新图表数据\r\n        this.updateChartData()\r\n\r\n        return Promise.resolve()\r\n      } catch (error) {\r\n        console.error('获取营养数据失败', error)\r\n        uni.showToast({\r\n          title: '获取数据失败，请稍后重试',\r\n          icon: 'none'\r\n        })\r\n        return Promise.reject(error)\r\n      }\r\n    },\r\n\r\n    // 更新图表数据\r\n    updateChartData() {\r\n      if (!this.nutritionTrend || !this.nutritionTrend.dateList || this.nutritionTrend.dateList.length === 0) {\r\n        return\r\n      }\r\n\r\n      // 更新卡路里图表数据\r\n      this.calorieChartData = {\r\n        categories: this.nutritionTrend.dateList.map(this.formatChartDate),\r\n        series: [\r\n          {\r\n            name: '热量',\r\n            data: this.nutritionTrend.calorieList\r\n          }\r\n        ]\r\n      }\r\n\r\n      // 更新营养素图表数据\r\n      this.nutrientChartData = {\r\n        categories: this.nutritionTrend.dateList.map(this.formatChartDate),\r\n        series: [\r\n          {\r\n            name: '蛋白质',\r\n            data: this.nutritionTrend.proteinList\r\n          },\r\n          {\r\n            name: '碳水',\r\n            data: this.nutritionTrend.carbsList\r\n          },\r\n          {\r\n            name: '脂肪',\r\n            data: this.nutritionTrend.fatList\r\n          }\r\n        ]\r\n      }\r\n\r\n      // 延迟一下确保DOM更新后再触发图表重绘\r\n      this.$nextTick(() => {\r\n        // 手动触发图表组件刷新\r\n        if (this.$refs.calorieChart) {\r\n          // 通过改变reload属性强制图表重新渲染\r\n          this.$refs.calorieChart.reload = true\r\n        }\r\n        if (this.$refs.nutrientChart) {\r\n          this.$refs.nutrientChart.reload = true\r\n        }\r\n      })\r\n    },\r\n\r\n    // 切换日期\r\n    changeDate(days) {\r\n      const newDate = new Date(this.selectedDate);\r\n      newDate.setDate(newDate.getDate() + days);\r\n\r\n      // 检查是否超过今天\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n\r\n      // 规范化newDate，清除时间部分，只保留日期部分\r\n      newDate.setHours(0, 0, 0, 0);\r\n\r\n      if (newDate > today && days > 0) {\r\n        // 如果是向后选择且超过今天，则不执行并提示用户\r\n        uni.showToast({\r\n          title: '不能选择今天之后的日期',\r\n          icon: 'none',\r\n          duration: 2000\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.selectedDate = newDate;\r\n      this.datePickerValue = formatDate(newDate, 'yyyy-MM-dd');\r\n      // 刷新数据\r\n      this.fetchAllNutritionData();\r\n    },\r\n\r\n    // 日期选择器变化处理\r\n    onDateChange(e) {\r\n      this.datePickerValue = e.detail.value;\r\n      this.selectedDate = new Date(e.detail.value.replace(/-/g, '/'));\r\n      // 刷新数据\r\n      this.fetchAllNutritionData();\r\n    },\r\n\r\n    // 获取建议图标\r\n    getAdviceIcon(type) {\r\n      const icons = {\r\n        warning: '/static/icons/warning.png',\r\n        info: '/static/icons/info.png',\r\n        danger: '/static/icons/danger.png',\r\n        success: '/static/icons/success.png'\r\n      }\r\n      return icons[type] || icons.info\r\n    },\r\n\r\n    // 获取几个月前的日期\r\n    getDateMonthsAgo(months) {\r\n      const date = new Date()\r\n      date.setMonth(date.getMonth() - months)\r\n      return date.toISOString().split('T')[0]\r\n    },\r\n\r\n    // 仅刷新图表渲染，不获取新数据\r\n    refreshCharts() {\r\n      // 先更新一次图表数据，确保数据是最新的格式\r\n      this.updateChartData()\r\n\r\n      // 通过临时隐藏图表组件然后重新显示，强制重新渲染\r\n      this.showCalorieChart = false\r\n      this.showNutrientChart = false\r\n\r\n      // 这会触发watch，图表会在短暂延迟后重新显示\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.nutrition-analysis {\r\n  padding: 0 20rpx 20rpx;\r\n  background-color: #f8f8f8;\r\n  min-height: 100vh;\r\n}\r\n\r\n.safe-area {\r\n  height: 80rpx;\r\n  background-color: #f8f8f8;\r\n}\r\n\r\n.header {\r\n  padding: 0 30rpx;\r\n  height: 88rpx;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  position: relative;\r\n  margin-bottom: 20rpx;\r\n\r\n  .title {\r\n    font-size: 36rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n  }\r\n}\r\n\r\n// 日期选择器样式\r\n.date-selector {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 20rpx 30rpx;\r\n  background-color: #ffffff;\r\n  margin-bottom: 20rpx;\r\n  border-radius: 10rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n  z-index: 10; // 确保日期选择器在图表上方\r\n\r\n  .date-actions {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 100%;\r\n    padding: 0;\r\n\r\n    .date-arrow {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n\r\n      image {\r\n        width: 24rpx;\r\n        height: 24rpx;\r\n      }\r\n    }\r\n\r\n    .date-display {\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      padding: 10rpx 0;\r\n      max-width: 300rpx;\r\n\r\n      .date-title {\r\n        font-size: 40rpx;\r\n        font-weight: bold;\r\n        color: #333;\r\n        margin-bottom: 10rpx;\r\n      }\r\n\r\n      .date-value {\r\n        font-size: 28rpx;\r\n        color: #999;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.loading-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 300rpx;\r\n\r\n  .loading-box {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n\r\n    .loading-spinner {\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n      border: 4rpx solid #f3f3f3;\r\n      border-top: 4rpx solid #4CAF50;\r\n      border-radius: 50%;\r\n      animation: spin 1s linear infinite;\r\n      margin-bottom: 20rpx;\r\n    }\r\n\r\n    .loading-text {\r\n      font-size: 28rpx;\r\n      color: #666;\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.card {\r\n  background-color: #ffffff;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n  animation: fadeIn 0.5s ease-in-out;\r\n  position: relative; // 添加相对定位以确保z-index正常工作\r\n  z-index: 1; // 确保卡片在日期选择器下方\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: translateY(20rpx); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n\r\n  .section-title {\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n    position: relative;\r\n    padding-left: 20rpx;\r\n\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      left: 0;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      width: 8rpx;\r\n      height: 32rpx;\r\n      background-color: #4CAF50;\r\n      border-radius: 4rpx;\r\n    }\r\n  }\r\n\r\n  .date-info {\r\n    font-size: 24rpx;\r\n    color: #999;\r\n  }\r\n\r\n  .legend {\r\n    display: flex;\r\n\r\n    .legend-item {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-left: 20rpx;\r\n\r\n      .legend-color {\r\n        width: 20rpx;\r\n        height: 20rpx;\r\n        border-radius: 4rpx;\r\n        margin-right: 8rpx;\r\n      }\r\n\r\n      .legend-label {\r\n        font-size: 24rpx;\r\n        color: #666;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.nutrients-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 20rpx;\r\n\r\n  .nutrient-card {\r\n    background-color: #f9f9f9;\r\n    border-radius: 16rpx;\r\n    padding: 20rpx;\r\n    transition: all 0.3s ease;\r\n\r\n    &:active {\r\n      transform: scale(0.98);\r\n    }\r\n\r\n    .nutrient-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 16rpx;\r\n\r\n      .nutrient-label {\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        font-weight: 500;\r\n      }\r\n\r\n      .nutrient-value {\r\n        font-size: 28rpx;\r\n        font-weight: bold;\r\n        color: #333;\r\n      }\r\n    }\r\n\r\n    .progress-container {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .progress-bar {\r\n        flex: 1;\r\n        height: 20rpx;\r\n        background-color: #f0f0f0;\r\n        border-radius: 10rpx;\r\n        overflow: hidden;\r\n        margin-right: 16rpx;\r\n\r\n        .progress {\r\n          height: 100%;\r\n          border-radius: 10rpx;\r\n          transition: width 0.8s ease;\r\n        }\r\n      }\r\n\r\n      .percentage-text {\r\n        font-size: 24rpx;\r\n        color: #666;\r\n        width: 60rpx;\r\n        text-align: right;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.chart-container {\r\n  height: 380rpx;\r\n  padding-bottom: 20rpx;\r\n}\r\n\r\n// 为秋云图表组件添加样式\r\n.qiun-charts {\r\n  width: 100%;\r\n  height: 350rpx;\r\n  z-index: 1; // 确保图表在日期选择器下方\r\n}\r\n\r\n// 确保图表容器有足够的高度\r\n.trend-chart {\r\n  min-height: 450rpx;\r\n}\r\n\r\n.advice-content {\r\n  .advice-item {\r\n    display: flex;\r\n    margin-bottom: 30rpx;\r\n\r\n    .advice-icon {\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n      border-radius: 30rpx;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin-right: 20rpx;\r\n\r\n      &.info {\r\n        background-color: #E3F2FD;\r\n      }\r\n\r\n      &.warning {\r\n        background-color: #FFF3E0;\r\n      }\r\n\r\n      &.danger {\r\n        background-color: #FFEBEE;\r\n      }\r\n\r\n      &.success {\r\n        background-color: #E8F5E9;\r\n      }\r\n\r\n      image {\r\n        width: 36rpx;\r\n        height: 36rpx;\r\n      }\r\n    }\r\n\r\n    .advice-text {\r\n      flex: 1;\r\n\r\n      .advice-title {\r\n        font-size: 28rpx;\r\n        font-weight: bold;\r\n        color: #333;\r\n        margin-bottom: 8rpx;\r\n        display: block;\r\n      }\r\n\r\n      .advice-desc {\r\n        font-size: 26rpx;\r\n        color: #666;\r\n        line-height: 1.5;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751115964899\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}