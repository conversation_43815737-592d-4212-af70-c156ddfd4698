{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/health-report/index.vue?2804", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/health-report/index.vue?cd9c", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/health-report/index.vue?b1ef", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/health-report/index.vue?2c2b", "uni-app:///pages/health-report/index.vue", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/health-report/index.vue?b4de", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/health-report/index.vue?1657"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "qiunData<PERSON><PERSON><PERSON>", "data", "selectedDate", "startDate", "endDate", "formattedDate", "radarColors", "lastDataFetchTime", "dataCacheTime", "radarChartOpts", "color", "padding", "legend", "show", "dataLabel", "extra", "radar", "gridType", "gridColor", "labelColor", "max", "radarChartData", "categories", "series", "datePickerValue", "computed", "healthReport", "isLoading", "dataChanged", "dateTitle", "today", "selectedDateObj", "nutritionBalanceItems", "label", "value", "weeklyCompareItems", "icon", "lastWeek", "thisWeek", "change", "watch", "handler", "deep", "onLoad", "then", "onReady", "onPullDownRefresh", "uni", "onShow", "console", "methods", "fetchHealthReport", "fetchHealthReportData", "dateStr", "date", "Promise", "title", "updateRadarData", "name", "balance", "calculateChange", "formatChange", "getChangeIcon", "showDatePicker", "onDateChange", "changeDate", "getDateMonthsAgo", "navigateBack"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qXAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACwItnB;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAOA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;UACAC;QACA;QACAC;QACAC;UACAC;YACAC;YACAC;YACAC;YACAC;UACA;QACA;MACA;MACA;MACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC,0CACA;IACAC;IACAC;IACAC;EACA;IAEA;IACAC;MACA;MACA;MACAC;MAEA;MACAC;MAEA;MAEA;MACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA,QACA;QAAAC;QAAAC;QAAAxB;MAAA,GACA;QAAAuB;QAAAC;QAAAxB;MAAA,GACA;QAAAuB;QAAAC;QAAAxB;MAAA,EACA;IACA;IAEA;IACAyB;MAAA;MACA;MAEA,QACA;QACAF;QACAG;QACAC;QACAC;QACAC;MACA,GACA;QACAN;QACAG;QACAC;QACAC;QACAC;MACA,GACA;QACAN;QACAG;QACAC;QACAC;QACAC;MACA,GACA;QACAN;QACAG;QACAC;QACAC;QACAC;MACA,EACA;IACA;EAAA,EACA;EACAC;IACA;IACA;MACAC;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA,6BACAC;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EAAA,CACA;EACAC;IAAA;IACA;IACA;MACA;MACA;MACAC;IACA;EACA;EAEA;EACAC;IAAA;IACA;IACA;;IAEA;IACA;MACA;MACAC;MACA,6BACAL;QACA;QACA;;QAEA;QACA;UACA;QACA;MACA;IACA;MACA;MACAK;IACA;EACA;EACAC,yCACA;IACAC;EACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA;gBAAA,OACA;kBAAAC;gBAAA;cAAA;gBAEA;gBACA;gBAAA,iCAEAC;cAAA;gBAAA;gBAAA;gBAEAN;gBACAF;kBACAS;kBACApB;gBACA;gBAAA,iCACAmB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MACA;QACA;MACA;MAEA;MAEA;QACAnC;QACAC;UACAmC;UACAzD,OACA0D,sBACAA,oBACAA;QAEA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IAAA,CACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAX;;MAEA;MACA;MACAxB;MACA;QACAwB;MACA;;MAEA;MACA;MACA;QACAA;MACA;MAEA;MACA;MACA;MACA;IACA;IAEA;IACAY;MACA;MACAZ;MACA;IACA;IAEA;IACAa;MACApB;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;AC/aA;AAAA;AAAA;AAAA;AAA6oC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAjqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/health-report/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/health-report/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=3791311f&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/health-report/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=3791311f&\"", "var components\ntry {\n  components = {\n    qiunDataCharts: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts\" */ \"@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = !_vm.isLoading\n    ? _vm.__map(_vm.weeklyCompareItems, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.getChangeIcon(item.change)\n        var m1 = _vm.formatChange(item.change)\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"health-report\">\r\n    <!-- 顶部安全区域 -->\r\n    <view class=\"safe-area\"></view>\r\n\r\n    <!-- 顶部标题 -->\r\n    <view class=\"header\">\r\n      <view class=\"back-btn\" @click=\"navigateBack()\">\r\n        <image src=\"/static/icons/back.png\"></image>\r\n      </view>\r\n      <text class=\"title\">健康报告</text>\r\n    </view>\r\n\r\n    <!-- 日期选择器移至此处 -->\r\n    <view class=\"date-selector\">\r\n      <view class=\"date-actions\">\r\n        <view class=\"date-arrow\" @click=\"changeDate(-1)\">\r\n          <image src=\"/static/icons/arrow-left.png\"></image>\r\n        </view>\r\n        <picker mode=\"date\" :value=\"datePickerValue\" :start=\"startDate\" :end=\"endDate\" @change=\"onDateChange\">\r\n          <view class=\"date-display\">\r\n            <text class=\"date-title\">{{ dateTitle }}</text>\r\n            <text class=\"date-value\">{{ formattedDate }}</text>\r\n          </view>\r\n        </picker>\r\n        <view class=\"date-arrow\" @click=\"changeDate(1)\">\r\n          <image src=\"/static/icons/arrow-right.png\"></image>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 加载中 -->\r\n    <view v-if=\"isLoading\" class=\"loading-container\">\r\n      <view class=\"loading-box\">\r\n        <view class=\"loading-spinner\"></view>\r\n        <text class=\"loading-text\">加载中...</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <view v-else class=\"content-container\">\r\n      <!-- 健康评分卡片 -->\r\n      <view class=\"health-score card\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">健康评分</text>\r\n          <text class=\"date-info\">{{ formattedDate }}</text>\r\n        </view>\r\n\r\n        <view class=\"score-container\">\r\n          <view class=\"score-circle\">\r\n            <view class=\"score-value\">{{ healthReport.healthScore }}</view>\r\n            <view class=\"score-desc\">健康评分</view>\r\n          </view>\r\n\r\n          <view class=\"score-change\">\r\n            <view class=\"change-value\" :class=\"{ 'positive': healthReport.scoreChange > 0, 'negative': healthReport.scoreChange < 0 }\">\r\n              <text>{{ healthReport.scoreChange > 0 ? '+' : '' }}{{ healthReport.scoreChange }}</text>\r\n              <view class=\"change-icon\">\r\n                <image :src=\"healthReport.scoreChange >= 0 ? '/static/icons/up.png' : '/static/icons/down.png'\"></image>\r\n              </view>\r\n            </view>\r\n            <text class=\"change-label\">较上周</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 营养平衡雷达图 -->\r\n      <view class=\"nutrition-balance card\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">营养平衡</text>\r\n        </view>\r\n\r\n        <view class=\"radar-container\">\r\n          <!-- 使用秋云ucharts组件替换原canvas -->\r\n          <!-- qiun-data-charts是uni_modules下的秋云图表组件，支持更丰富的功能和更好的兼容性 -->\r\n          <qiun-data-charts\r\n            type=\"radar\"\r\n            :opts=\"radarChartOpts\"\r\n            :chartData=\"radarChartData\"\r\n            canvasId=\"balanceRadar\"\r\n            ref=\"balanceRadar\"\r\n          />\r\n        </view>\r\n\r\n        <view class=\"radar-legend\">\r\n          <view class=\"legend-item\" v-for=\"(item, index) in nutritionBalanceItems\" :key=\"index\">\r\n            <view class=\"legend-color\" :style=\"{ backgroundColor: item.color }\"></view>\r\n            <text class=\"legend-label\">{{ item.label }}</text>\r\n            <text class=\"legend-value\">{{ item.value }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 营养周对比卡片 -->\r\n      <view class=\"nutrition-compare card\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">本周 vs 上周</text>\r\n        </view>\r\n\r\n        <view class=\"compare-container\">\r\n          <view class=\"compare-item\" v-for=\"(item, index) in weeklyCompareItems\" :key=\"index\">\r\n            <view class=\"compare-header\">\r\n              <view class=\"compare-icon\">\r\n                <image :src=\"item.icon\"></image>\r\n              </view>\r\n              <text class=\"compare-label\">{{ item.label }}</text>\r\n            </view>\r\n\r\n            <view class=\"compare-data\">\r\n              <view class=\"data-col\">\r\n                <text class=\"data-value\">{{ item.lastWeek }}</text>\r\n                <text class=\"data-desc\">上周</text>\r\n              </view>\r\n\r\n              <view class=\"direction-indicator\">\r\n                <view class=\"indicator-arrow\" :class=\"{ 'increase': item.change > 0, 'decrease': item.change < 0, 'neutral': item.change === 0 }\">\r\n                  <image :src=\"getChangeIcon(item.change)\"></image>\r\n                </view>\r\n                <text class=\"change-rate\" :class=\"{ 'increase': item.change > 0, 'decrease': item.change < 0, 'neutral': item.change === 0 }\">\r\n                  {{ formatChange(item.change) }}\r\n                </text>\r\n              </view>\r\n\r\n              <view class=\"data-col\">\r\n                <text class=\"data-value highlight\">{{ item.thisWeek }}</text>\r\n                <text class=\"data-desc\">本周</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { formatDate } from '@/utils/date.js'\r\nimport { mapActions, mapGetters } from 'vuex'\r\n// 移除原uCharts导入\r\n// import uCharts from '@/components/u-charts/u-charts.js'\r\n// 导入秋云ucharts组件\r\n// qiun-data-charts是一个高性能的图表组件，支持各端平台，包括H5、小程序、APP等\r\nimport qiunDataCharts from \"uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue\"\r\n\r\nexport default {\r\n  components: {\r\n    qiunDataCharts\r\n  },\r\n  data() {\r\n    return {\r\n      selectedDate: new Date(),\r\n      startDate: this.getDateMonthsAgo(3), // 三个月前\r\n      endDate: new Date().toISOString().split('T')[0], // 今天\r\n      formattedDate: '',\r\n      // 移除radarChartInstance\r\n      // radarChartInstance: null,\r\n      radarColors: ['#4CAF50', '#8BC34A', '#CDDC39', '#FFEB3B', '#FFC107'],\r\n      // 数据缓存时间戳，用于判断是否需要重新获取数据\r\n      lastDataFetchTime: 0,\r\n      // 缓存刷新间隔，单位为毫秒（默认5分钟）\r\n      dataCacheTime: 5 * 60 * 1000,\r\n\r\n      // 添加雷达图配置\r\n      radarChartOpts: {\r\n        color: ['#4CAF50'],\r\n        padding: [30, 30, 30, 30],\r\n        legend: {\r\n          show: false\r\n        },\r\n        dataLabel: true,\r\n        extra: {\r\n          radar: {\r\n            gridType: 'polygon',\r\n            gridColor: '#CCCCCC',\r\n            labelColor: '#666666',\r\n            max: 150\r\n          }\r\n        }\r\n      },\r\n      // 添加雷达图数据\r\n      radarChartData: {\r\n        categories: ['蛋白质', '碳水', '脂肪'],\r\n        series: []\r\n      },\r\n      datePickerValue: ''\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters({\r\n      healthReport: 'nutrition/healthReport',\r\n      isLoading: 'nutrition/isLoading',\r\n      dataChanged: 'nutrition/dataChanged' // 添加数据变更标志\r\n    }),\r\n\r\n    // 日期标题，显示今天/昨天/前天\r\n    dateTitle() {\r\n      // 判断是否是今天、昨天或前天\r\n      const today = new Date()\r\n      today.setHours(0, 0, 0, 0)\r\n\r\n      const selectedDateObj = new Date(this.selectedDate)\r\n      selectedDateObj.setHours(0, 0, 0, 0)\r\n\r\n      const diffDays = Math.round((selectedDateObj - today) / (1000 * 60 * 60 * 24))\r\n\r\n      if (diffDays === 0) return '今天'\r\n      if (diffDays === -1) return '昨天'\r\n      if (diffDays === -2) return '前天'\r\n\r\n      return '' // 其他日期不显示特殊标题\r\n    },\r\n\r\n    // 格式化营养平衡数据\r\n    nutritionBalanceItems() {\r\n      const balance = this.healthReport.nutritionBalance || {};\r\n      return [\r\n        { label: '蛋白质', value: `${balance.protein || 0}%`, color: this.radarColors[0] },\r\n        { label: '碳水化合物', value: `${balance.carbs || 0}%`, color: this.radarColors[1] },\r\n        { label: '脂肪', value: `${balance.fat || 0}%`, color: this.radarColors[2] }\r\n      ];\r\n    },\r\n\r\n    // 格式化每周对比数据\r\n    weeklyCompareItems() {\r\n      const weekly = this.healthReport.weeklyProgress || {};\r\n\r\n      return [\r\n        {\r\n          label: '热量',\r\n          icon: '/static/icons/calorie.png',\r\n          lastWeek: weekly.calorie?.lastWeek || 0,\r\n          thisWeek: weekly.calorie?.thisWeek || 0,\r\n          change: this.calculateChange(weekly.calorie?.lastWeek || 0, weekly.calorie?.thisWeek || 0)\r\n        },\r\n        {\r\n          label: '蛋白质',\r\n          icon: '/static/icons/protein.png',\r\n          lastWeek: weekly.protein?.lastWeek || 0,\r\n          thisWeek: weekly.protein?.thisWeek || 0,\r\n          change: this.calculateChange(weekly.protein?.lastWeek || 0, weekly.protein?.thisWeek || 0)\r\n        },\r\n        {\r\n          label: '碳水',\r\n          icon: '/static/icons/carbs.png',\r\n          lastWeek: weekly.carbs?.lastWeek || 0,\r\n          thisWeek: weekly.carbs?.thisWeek || 0,\r\n          change: this.calculateChange(weekly.carbs?.lastWeek || 0, weekly.carbs?.thisWeek || 0)\r\n        },\r\n        {\r\n          label: '脂肪',\r\n          icon: '/static/icons/fat.png',\r\n          lastWeek: weekly.fat?.lastWeek || 0,\r\n          thisWeek: weekly.fat?.thisWeek || 0,\r\n          change: this.calculateChange(weekly.fat?.lastWeek || 0, weekly.fat?.thisWeek || 0)\r\n        }\r\n      ];\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听健康报告数据变化，更新雷达图\r\n    'healthReport.nutritionBalance': {\r\n      handler(newValue) {\r\n        if (newValue) {\r\n          this.updateRadarData()\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  onLoad() {\r\n    this.formattedDate = formatDate(this.selectedDate, 'yyyy年MM月dd日')\r\n    this.datePickerValue = formatDate(this.selectedDate, 'yyyy-MM-dd') // 初始化日期选择器的值\r\n    this.fetchHealthReportData()\r\n      .then(() => {\r\n        // 更新数据缓存时间戳\r\n        this.lastDataFetchTime = Date.now()\r\n      })\r\n  },\r\n  onReady() {\r\n    // 不再需要手动初始化图表\r\n    // setTimeout(() => {\r\n    //   this.initRadarChart()\r\n    // }, 300)\r\n  },\r\n  onPullDownRefresh() {\r\n    // 下拉刷新\r\n    this.fetchHealthReportData().then(() => {\r\n      // 更新数据缓存时间戳\r\n      this.lastDataFetchTime = Date.now()\r\n      uni.stopPullDownRefresh()\r\n    })\r\n  },\r\n\r\n  // 添加onShow生命周期，当从其他页面返回时更新数据\r\n  onShow() {\r\n    const currentTime = Date.now()\r\n    const timeSinceLastFetch = currentTime - this.lastDataFetchTime\r\n\r\n    // 检查是否需要刷新数据：首次加载、缓存过期或数据已变更\r\n    if (this.lastDataFetchTime === 0 || timeSinceLastFetch > this.dataCacheTime || this.dataChanged) {\r\n      // 如果是首次加载、缓存已过期或数据已变更，则重新获取数据\r\n      console.log('健康报告：刷新数据', this.dataChanged ? '（数据已变更）' : '（缓存过期）')\r\n      this.fetchHealthReportData()\r\n        .then(() => {\r\n          // 更新数据缓存时间戳\r\n          this.lastDataFetchTime = Date.now()\r\n\r\n          // 重置数据变更标志\r\n          if (this.dataChanged) {\r\n            this.$store.dispatch('nutrition/setDataChanged', false)\r\n          }\r\n        })\r\n    } else {\r\n      // 缓存有效，不重新请求数据\r\n      console.log('健康报告：使用缓存数据，跳过网络请求')\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions({\r\n      fetchHealthReport: 'nutrition/fetchHealthReport'\r\n    }),\r\n\r\n    // 获取健康报告数据\r\n    async fetchHealthReportData() {\r\n      try {\r\n        const dateStr = formatDate(this.selectedDate, 'yyyy-MM-dd')\r\n        await this.fetchHealthReport({ date: dateStr })\r\n\r\n        // 更新雷达图数据\r\n        this.updateRadarData()\r\n\r\n        return Promise.resolve()\r\n      } catch (error) {\r\n        console.error('获取健康报告数据失败', error)\r\n        uni.showToast({\r\n          title: '获取数据失败，请稍后重试',\r\n          icon: 'none'\r\n        })\r\n        return Promise.reject(error)\r\n      }\r\n    },\r\n\r\n    // 更新雷达图数据\r\n    updateRadarData() {\r\n      if (!this.healthReport.nutritionBalance) {\r\n        return\r\n      }\r\n\r\n      const balance = this.healthReport.nutritionBalance\r\n\r\n      this.radarChartData = {\r\n        categories: ['蛋白质', '碳水', '脂肪'],\r\n        series: [{\r\n          name: '营养平衡',\r\n          data: [\r\n            balance.protein || 0,\r\n            balance.carbs || 0,\r\n            balance.fat || 0\r\n          ]\r\n        }]\r\n      }\r\n    },\r\n\r\n    // 计算变化率\r\n    calculateChange(lastWeek, thisWeek) {\r\n      if (lastWeek === 0) return thisWeek > 0 ? 100 : 0;\r\n      return ((thisWeek - lastWeek) / lastWeek) * 100;\r\n    },\r\n\r\n    // 格式化变化率\r\n    formatChange(change) {\r\n      if (change === 0) return '持平';\r\n      return (change > 0 ? '+' : '') + change.toFixed(1) + '%';\r\n    },\r\n\r\n    // 获取变化图标\r\n    getChangeIcon(change) {\r\n      if (change > 0) return '/static/icons/up.png';\r\n      if (change < 0) return '/static/icons/up.png'; // 使用up.png但在CSS中旋转\r\n      return '/static/icons/equal.png';\r\n    },\r\n\r\n    // 显示日期选择器\r\n    showDatePicker() {\r\n      // 使用picker不需要特殊处理，点击日期文本时已经会自动显示\r\n    },\r\n\r\n    // 日期选择响应\r\n    onDateChange(e) {\r\n      this.selectedDate = new Date(e.detail.value);\r\n      this.datePickerValue = e.detail.value;\r\n      this.formattedDate = formatDate(this.selectedDate, 'yyyy年MM月dd日');\r\n      this.fetchHealthReportData();\r\n    },\r\n\r\n    // 切换日期（上一天/下一天）\r\n    changeDate(step) {\r\n      const date = new Date(this.selectedDate);\r\n      date.setDate(date.getDate() + step);\r\n\r\n      // 不能超过今天\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      if (date > today) {\r\n        date.setTime(today.getTime());\r\n      }\r\n\r\n      // 不能早于开始日期\r\n      const startDateObj = new Date(this.startDate);\r\n      if (date < startDateObj) {\r\n        date.setTime(startDateObj.getTime());\r\n      }\r\n\r\n      this.selectedDate = date;\r\n      this.datePickerValue = formatDate(date, 'yyyy-MM-dd');\r\n      this.formattedDate = formatDate(date, 'yyyy年MM月dd日');\r\n      this.fetchHealthReportData();\r\n    },\r\n\r\n    // 获取几个月前的日期\r\n    getDateMonthsAgo(months) {\r\n      const date = new Date()\r\n      date.setMonth(date.getMonth() - months)\r\n      return date.toISOString().split('T')[0]\r\n    },\r\n\r\n    // 返回上一页\r\n    navigateBack() {\r\n      uni.navigateBack()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.health-report {\r\n  padding: 0 20rpx 20rpx;\r\n  background-color: #f8f8f8;\r\n  min-height: 100vh;\r\n}\r\n\r\n.safe-area {\r\n  height: 80rpx;\r\n  background-color: #f8f8f8;\r\n}\r\n\r\n.header {\r\n  padding: 0 30rpx;\r\n  height: 88rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  position: relative;\r\n\r\n  .back-btn {\r\n    width: 60rpx;\r\n    height: 60rpx;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n\r\n    image {\r\n      width: 36rpx;\r\n      height: 36rpx;\r\n    }\r\n  }\r\n\r\n  .title {\r\n    flex: 1;\r\n    text-align: center;\r\n    font-size: 36rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n  }\r\n}\r\n\r\n.loading-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 300rpx;\r\n\r\n  .loading-box {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n\r\n    .loading-spinner {\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n      border: 4rpx solid #f3f3f3;\r\n      border-top: 4rpx solid #4CAF50;\r\n      border-radius: 50%;\r\n      animation: spin 1s linear infinite;\r\n      margin-bottom: 20rpx;\r\n    }\r\n\r\n    .loading-text {\r\n      font-size: 28rpx;\r\n      color: #666;\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.content-container {\r\n  padding-bottom: 30rpx;\r\n}\r\n\r\n.date-selector {\r\n  background-color: #ffffff;\r\n  border-radius: 20rpx;\r\n  padding: 16rpx 24rpx;\r\n  margin: 0 20rpx 20rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n\r\n  .date-actions {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .date-arrow {\r\n      width: 56rpx;\r\n      height: 56rpx;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      background-color: #f8f8f8;\r\n      border-radius: 28rpx;\r\n\r\n      image {\r\n        width: 30rpx;\r\n        height: 30rpx;\r\n      }\r\n    }\r\n\r\n    .date-display {\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      .date-title {\r\n        font-size: 24rpx;\r\n        color: #666;\r\n        margin-bottom: 4rpx;\r\n      }\r\n\r\n      .date-value {\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.card {\r\n  background-color: #ffffff;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n  animation: fadeIn 0.5s ease-in-out;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: translateY(20rpx); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n\r\n  .section-title {\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n    position: relative;\r\n    padding-left: 20rpx;\r\n\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      left: 0;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      width: 8rpx;\r\n      height: 32rpx;\r\n      background-color: #4CAF50;\r\n      border-radius: 4rpx;\r\n    }\r\n  }\r\n\r\n  .date-info {\r\n    font-size: 24rpx;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.health-score {\r\n  .score-container {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-around;\r\n    padding: 20rpx 0;\r\n\r\n    .score-circle {\r\n      width: 200rpx;\r\n      height: 200rpx;\r\n      border-radius: 100rpx;\r\n      background: linear-gradient(135deg, #4CAF50, #8BC34A);\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: center;\r\n      align-items: center;\r\n      box-shadow: 0 8rpx 16rpx rgba(76, 175, 80, 0.2);\r\n      animation: pulse 2s infinite;\r\n\r\n      .score-value {\r\n        font-size: 60rpx;\r\n        font-weight: bold;\r\n        color: #fff;\r\n        line-height: 1;\r\n        margin-bottom: 10rpx;\r\n      }\r\n\r\n      .score-desc {\r\n        font-size: 24rpx;\r\n        color: rgba(255, 255, 255, 0.8);\r\n      }\r\n    }\r\n\r\n    .score-change {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n\r\n      .change-value {\r\n        display: flex;\r\n        align-items: center;\r\n        font-size: 48rpx;\r\n        font-weight: bold;\r\n        margin-bottom: 10rpx;\r\n\r\n        &.positive {\r\n          color: #4CAF50;\r\n        }\r\n\r\n        &.negative {\r\n          color: #F44336;\r\n        }\r\n\r\n        .change-icon {\r\n          width: 32rpx;\r\n          height: 32rpx;\r\n          margin-left: 8rpx;\r\n\r\n          image {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n\r\n      .change-label {\r\n        font-size: 24rpx;\r\n        color: #999;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 8rpx 16rpx rgba(76, 175, 80, 0.2);\r\n  }\r\n  50% {\r\n    box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.4);\r\n  }\r\n  100% {\r\n    box-shadow: 0 8rpx 16rpx rgba(76, 175, 80, 0.2);\r\n  }\r\n}\r\n\r\n.nutrition-balance {\r\n  .radar-container {\r\n    height: 500rpx;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n\r\n    // 不再需要\r\n    // .radar-chart {\r\n    //   width: 100%;\r\n    //   height: 100%;\r\n    // }\r\n  }\r\n\r\n  // 添加秋云图表组件样式\r\n  :deep(.qiun-charts) {\r\n    width: 100%;\r\n    height: 500rpx;\r\n  }\r\n\r\n  .radar-legend {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: space-around;\r\n    margin-top: 30rpx;\r\n\r\n    .legend-item {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 20rpx;\r\n      width: 30%;\r\n\r\n      .legend-color {\r\n        width: 20rpx;\r\n        height: 20rpx;\r\n        border-radius: 4rpx;\r\n        margin-right: 10rpx;\r\n      }\r\n\r\n      .legend-label {\r\n        font-size: 24rpx;\r\n        color: #666;\r\n        margin-right: 10rpx;\r\n      }\r\n\r\n      .legend-value {\r\n        font-size: 24rpx;\r\n        color: #333;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.nutrition-compare {\r\n  .compare-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .compare-item {\r\n      margin-bottom: 30rpx;\r\n\r\n      &:last-child {\r\n        margin-bottom: 0;\r\n      }\r\n\r\n      .compare-header {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 15rpx;\r\n\r\n        .compare-icon {\r\n          width: 40rpx;\r\n          height: 40rpx;\r\n          margin-right: 10rpx;\r\n\r\n          image {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n\r\n        .compare-label {\r\n          font-size: 28rpx;\r\n          color: #333;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n\r\n      .compare-data {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        background-color: #f9f9f9;\r\n        border-radius: 12rpx;\r\n        padding: 20rpx;\r\n\r\n        .data-col {\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          width: 30%;\r\n\r\n          .data-value {\r\n            font-size: 32rpx;\r\n            color: #666;\r\n            margin-bottom: 5rpx;\r\n\r\n            &.highlight {\r\n              color: #333;\r\n              font-weight: bold;\r\n            }\r\n          }\r\n\r\n          .data-desc {\r\n            font-size: 24rpx;\r\n            color: #999;\r\n          }\r\n        }\r\n\r\n        .direction-indicator {\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n\r\n          .indicator-arrow {\r\n            width: 40rpx;\r\n            height: 40rpx;\r\n            margin-bottom: 8rpx;\r\n\r\n            &.increase {\r\n              image {\r\n                width: 100%;\r\n                height: 100%;\r\n                transform: rotate(0deg);\r\n              }\r\n            }\r\n\r\n            &.decrease {\r\n              image {\r\n                width: 100%;\r\n                height: 100%;\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &.neutral {\r\n              image {\r\n                width: 100%;\r\n                height: 100%;\r\n                transform: rotate(0deg);\r\n              }\r\n            }\r\n\r\n            image {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n\r\n          .change-rate {\r\n            font-size: 24rpx;\r\n\r\n            &.increase {\r\n              color: #4CAF50;\r\n            }\r\n\r\n            &.decrease {\r\n              color: #F44336;\r\n            }\r\n\r\n            &.neutral {\r\n              color: #999;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751115964934\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}