{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/login/index.vue?5271", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/login/index.vue?760f", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/login/index.vue?1622", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/login/index.vue?8750", "uni-app:///pages/login/index.vue", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/login/index.vue?e69b", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/login/index.vue?566d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loginType", "email", "password", "wxUserInfo", "showPassword", "computed", "loading", "methods", "login", "wechatLoginAction", "emailLogin", "uni", "title", "icon", "then", "setTimeout", "url", "catch", "getWXUserInfo", "provider", "success", "fail", "wechatLoginWithCode", "code", "encryptedData", "iv", "showUserAgreement", "content", "showCancel", "showPrivacyPolicy", "togglePassword", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChBA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACoDtnB;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC,4BACA;IACAC;MAAA;IAAA;EACA,GACA;EACAC,yCACA;IACAC;IACAC;EACA;IACAC;MACA;QACAC;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACAZ;QACAC;MACA,GACAY;QACAH;UACAC;UACAC;QACA;;QAEA;QACAE;UACAJ;YACAK;UACA;QACA;MACA,GACAC;QACA;QACAN;UACAC;UACAC;QACA;QACA;MACA;IACA;IACA;IACAK;MAAA;MACA;QACAP;UACAC;UACAC;QACA;QACA;MACA;MAEA;;MAEA;MACAF;QACAQ;QACAC;UACA;YACA;UACA;YACAT;cACAC;cACAC;YACA;UACA;QACA;QACAQ;UACAV;YACAC;YACAC;UACA;QACA;MACA;IACA;IACA;IACAS;MACA;MACA;QACAC;QACAC;QACAC;MACA,GACAX;QACAH;UACAC;UACAC;QACA;;QAEA;QACAE;UACAJ;YACAK;UACA;QACA;MACA,GACAC;QACAN;UACAC;UACAC;QACA;MACA;IACA;IACAa;MACAf;QACAC;QACAe;QACAC;MACA;IACA;IACAC;MACAlB;QACAC;QACAe;QACAC;MACA;IACA;IACAE;MACAC;MACA;MACAA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;AC/LA;AAAA;AAAA;AAAA;AAA6oC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAjqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4586967a&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4586967a&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.loginType = \"wechat\"\n    }\n    _vm.e1 = function ($event) {\n      _vm.loginType = \"email\"\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"login\">\r\n    <!-- 自定义导航栏，只显示标题 -->\r\n    <view class=\"custom-nav\">\r\n      <view class=\"nav-title\">登录</view>\r\n    </view>\r\n\r\n    <view class=\"logo-container\">\r\n      <image class=\"logo\" src=\"/static/images/logo.png\"></image>\r\n      <text class=\"app-name\">饮食记录</text>\r\n    </view>\r\n\r\n    <view class=\"login-form\" v-if=\"loginType === 'email'\">\r\n      <view class=\"form-item\">\r\n        <input class=\"input\" v-model=\"email\" placeholder=\"请输入邮箱\" />\r\n      </view>\r\n      <view class=\"form-item password-input\">\r\n        <input class=\"input\" v-model=\"password\" :password=\"!showPassword\" placeholder=\"请输入密码\" />\r\n        <view class=\"toggle-password\" @click.stop=\"togglePassword\">\r\n          <image :src=\"showPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'\" class=\"eye-icon\" mode=\"aspectFit\"></image>\r\n        </view>\r\n      </view>\r\n      <button class=\"login-btn\" @click=\"emailLogin\" :disabled=\"loading\">\r\n        <text v-if=\"!loading\">登录</text>\r\n        <view class=\"loading\" v-else></view>\r\n      </button>\r\n      <view class=\"switch-login-type\" @click=\"loginType = 'wechat'\">\r\n        <text>使用微信一键登录</text>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"wechat-login\" v-else>\r\n      <button class=\"wechat-btn\" open-type=\"getUserInfo\" @getuserinfo=\"getWXUserInfo\" :disabled=\"loading\">\r\n        <image src=\"/static/icons/wechat.png\" class=\"wechat-icon\"></image>\r\n        <text v-if=\"!loading\">微信一键登录</text>\r\n        <view class=\"loading\" v-else></view>\r\n      </button>\r\n      <view class=\"switch-login-type\" @click=\"loginType = 'email'\">\r\n        <text>使用邮箱密码登录</text>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"agreement\">\r\n      <text class=\"agreement-text\">登录即表示您同意</text>\r\n      <text class=\"agreement-link\" @click=\"showUserAgreement\">《用户协议》</text>\r\n      <text class=\"agreement-text\">和</text>\r\n      <text class=\"agreement-link\" @click=\"showPrivacyPolicy\">《隐私政策》</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { mapActions, mapState } from 'vuex'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loginType: 'wechat', // 'wechat' 或 'email'\r\n      email: '',\r\n      password: '',\r\n      wxUserInfo: null,\r\n      showPassword: false\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      loading: state => state.user.loading\r\n    })\r\n  },\r\n  methods: {\r\n    ...mapActions({\r\n      login: 'user/login',\r\n      wechatLoginAction: 'user/wechatLogin'\r\n    }),\r\n    emailLogin() {\r\n      if (!this.email || !this.password) {\r\n        uni.showToast({\r\n          title: '请输入邮箱和密码',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      // 调用登录接口\r\n      this.login({\r\n        email: this.email,\r\n        password: this.password\r\n      })\r\n        .then(() => {\r\n          uni.showToast({\r\n            title: '登录成功',\r\n            icon: 'success'\r\n          })\r\n\r\n          // 确保使用reLaunch而不是switchTab，彻底重新加载首页\r\n          setTimeout(() => {\r\n            uni.reLaunch({\r\n              url: '/pages/index/index'\r\n            })\r\n          }, 1500)\r\n        })\r\n        .catch(err => {\r\n          // 显示具体的错误信息\r\n          uni.showToast({\r\n            title: err.message || '登录失败',\r\n            icon: 'none'\r\n          })\r\n          // 不再进行页面跳转\r\n        })\r\n    },\r\n    // 微信登录第一步：获取用户信息\r\n    getWXUserInfo(e) {\r\n      if (e.detail.errMsg !== 'getUserInfo:ok') {\r\n        uni.showToast({\r\n          title: '授权失败',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      this.wxUserInfo = e.detail\r\n\r\n      // 获取微信登录凭证\r\n      uni.login({\r\n        provider: 'weixin',\r\n        success: (loginRes) => {\r\n          if (loginRes.code) {\r\n            this.wechatLoginWithCode(loginRes.code)\r\n          } else {\r\n            uni.showToast({\r\n              title: '微信登录失败',\r\n              icon: 'none'\r\n            })\r\n          }\r\n        },\r\n        fail: () => {\r\n          uni.showToast({\r\n            title: '微信登录失败',\r\n            icon: 'none'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 微信登录第二步：使用code调用后端接口\r\n    wechatLoginWithCode(code) {\r\n      // 调用微信登录接口\r\n      this.wechatLoginAction({\r\n        code: code,\r\n        encryptedData: this.wxUserInfo.encryptedData,\r\n        iv: this.wxUserInfo.iv\r\n      })\r\n        .then(() => {\r\n          uni.showToast({\r\n            title: '登录成功',\r\n            icon: 'success'\r\n          })\r\n\r\n          // 确保使用reLaunch而不是switchTab，彻底重新加载首页\r\n          setTimeout(() => {\r\n            uni.reLaunch({\r\n              url: '/pages/index/index'\r\n            })\r\n          }, 1500)\r\n        })\r\n        .catch(err => {\r\n          uni.showToast({\r\n            title: err.message || '登录失败',\r\n            icon: 'none'\r\n          })\r\n        })\r\n    },\r\n    showUserAgreement() {\r\n      uni.showModal({\r\n        title: '用户协议',\r\n        content: '这是用户协议内容...',\r\n        showCancel: false\r\n      })\r\n    },\r\n    showPrivacyPolicy() {\r\n      uni.showModal({\r\n        title: '隐私政策',\r\n        content: '这是隐私政策内容...',\r\n        showCancel: false\r\n      })\r\n    },\r\n    togglePassword() {\r\n      console.log('togglePassword called, current state:', this.showPassword)\r\n      this.showPassword = !this.showPassword\r\n      console.log('new state:', this.showPassword)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.login {\r\n  min-height: 100vh;\r\n  padding: 40rpx;\r\n  padding-top: 0; /* 移除上边距，为导航栏留出空间 */\r\n  background-color: #ffffff;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n/* 自定义导航栏样式 */\r\n.custom-nav {\r\n  width: 100%;\r\n  padding-top: var(--status-bar-height, 20px);\r\n  height: 70px;\r\n  display: flex;\r\n  align-items: flex-end;\r\n  justify-content: center;\r\n  background-color: #ffffff;\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 999;\r\n}\r\n\r\n.nav-title {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  padding-bottom: 15px;\r\n}\r\n\r\n.logo-container {\r\n  margin-top: calc(var(--status-bar-height, 20px) + 70px + 40rpx);\r\n  margin-bottom: 60rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n\r\n  .logo {\r\n    width: 160rpx;\r\n    height: 160rpx;\r\n    margin-bottom: 20rpx;\r\n  }\r\n\r\n  .app-name {\r\n    font-size: 36rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n  }\r\n}\r\n\r\n.login-form {\r\n  width: 100%;\r\n\r\n  .form-item {\r\n    margin-bottom: 30rpx;\r\n\r\n    .input {\r\n      width: 100%;\r\n      height: 90rpx;\r\n      background-color: #f8f8f8;\r\n      border-radius: 45rpx;\r\n      padding: 0 40rpx;\r\n      font-size: 28rpx;\r\n    }\r\n  }\r\n\r\n  .login-btn {\r\n    width: 100%;\r\n    height: 90rpx;\r\n    background-color: #4CAF50;\r\n    color: #ffffff;\r\n    border-radius: 45rpx;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    margin-top: 40rpx;\r\n    margin-bottom: 30rpx;\r\n\r\n    &[disabled] {\r\n      background-color: #a5d6a7;\r\n    }\r\n  }\r\n}\r\n\r\n.wechat-login {\r\n  width: 100%;\r\n\r\n  .wechat-btn {\r\n    width: 100%;\r\n    height: 90rpx;\r\n    background-color: #07C160;\r\n    color: #ffffff;\r\n    border-radius: 45rpx;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    margin-bottom: 30rpx;\r\n\r\n    &[disabled] {\r\n      background-color: #7fccb5;\r\n    }\r\n\r\n    .wechat-icon {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      margin-right: 10rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.switch-login-type {\r\n  text-align: center;\r\n  margin-bottom: 40rpx;\r\n\r\n  text {\r\n    font-size: 28rpx;\r\n    color: #4CAF50;\r\n  }\r\n}\r\n\r\n.agreement {\r\n  position: absolute;\r\n  bottom: 60rpx;\r\n  left: 0;\r\n  right: 0;\r\n  text-align: center;\r\n\r\n  .agreement-text {\r\n    font-size: 24rpx;\r\n    color: #999;\r\n  }\r\n\r\n  .agreement-link {\r\n    font-size: 24rpx;\r\n    color: #4CAF50;\r\n  }\r\n}\r\n\r\n.loading {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  border: 4rpx solid #ffffff;\r\n  border-radius: 50%;\r\n  border-top-color: transparent;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n.password-input {\r\n  position: relative;\r\n\r\n  .toggle-password {\r\n    position: absolute;\r\n    right: 40rpx;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    width: 60rpx;\r\n    height: 60rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 2;\r\n    background-color: transparent;\r\n\r\n    .eye-icon {\r\n      width: 32rpx;\r\n      height: 32rpx;\r\n    }\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751115964937\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}