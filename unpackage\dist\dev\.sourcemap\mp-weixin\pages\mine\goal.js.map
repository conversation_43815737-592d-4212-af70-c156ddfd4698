{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/goal.vue?ac0b", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/goal.vue?49b9", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/goal.vue?4294", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/goal.vue?953f", "uni-app:///pages/mine/goal.vue", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/goal.vue?9152", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/goal.vue?fcb6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "calorieTarget", "nutritionGoals", "protein", "carbs", "fat", "dietPreferences", "computed", "userInfo", "storeNutritionGoals", "storeDietPreferences", "onLoad", "methods", "updateUserInfo", "updateNutritionGoal", "getNutritionGoal", "initData", "uni", "title", "mask", "console", "icon", "togglePreference", "onSwitchChange", "saveGoals", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAimB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACoErnB;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC,4BACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA,GACA;EACAC;IACA;EACA;EACAC,yCACA;IACAC;IACAC;IACAC;EACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;kBACAC;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACA;;gBAEA;gBACA;kBACAhB;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAe;gBACAH;kBACAC;kBACAG;gBACA;cAAA;gBAAA;gBAEAJ;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAK;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAP;kBACAC;kBACAG;gBACA;gBAAA;cAAA;gBAAA;gBAKA;gBACAJ;kBACAC;kBACAC;gBACA;;gBAEA;gBACAnB;kBACAC;kBACAC;oBACAC;oBACAC;oBACAC;kBACA;kBACAC;gBACA,GAEA;gBAAA;gBAAA,OACA;cAAA;gBAEAW;kBACAC;kBACAG;gBACA;gBAEAI;kBACAR;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAG;gBACAH;kBACAC;kBACAG;gBACA;cAAA;gBAAA;gBAEAJ;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;AC3LA;AAAA;AAAA;AAAA;AAA4oC,CAAgB,6lCAAG,EAAC,C;;;;;;;;;;;ACAhqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/goal.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/goal.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./goal.vue?vue&type=template&id=49f37ba6&\"\nvar renderjs\nimport script from \"./goal.vue?vue&type=script&lang=js&\"\nexport * from \"./goal.vue?vue&type=script&lang=js&\"\nimport style0 from \"./goal.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/goal.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goal.vue?vue&type=template&id=49f37ba6&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goal.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goal.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"goal-setting\">\r\n    <view class=\"section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">基础目标</text>\r\n      </view>\r\n\r\n      <view class=\"form-item\">\r\n        <text class=\"form-label\">每日热量目标</text>\r\n        <view class=\"form-input\">\r\n          <input type=\"number\" v-model=\"calorieTarget\" placeholder=\"输入目标热量\" />\r\n          <text class=\"unit\">千卡</text>\r\n        </view>\r\n      </view>\r\n\r\n\r\n    </view>\r\n\r\n    <view class=\"section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">营养素目标</text>\r\n      </view>\r\n\r\n      <view class=\"form-item\">\r\n        <text class=\"form-label\">蛋白质</text>\r\n        <view class=\"form-input\">\r\n          <input type=\"number\" v-model=\"nutritionGoals.protein\" placeholder=\"输入蛋白质目标\" />\r\n          <text class=\"unit\">g</text>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"form-item\">\r\n        <text class=\"form-label\">碳水化合物</text>\r\n        <view class=\"form-input\">\r\n          <input type=\"number\" v-model=\"nutritionGoals.carbs\" placeholder=\"输入碳水目标\" />\r\n          <text class=\"unit\">g</text>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"form-item\">\r\n        <text class=\"form-label\">脂肪</text>\r\n        <view class=\"form-input\">\r\n          <input type=\"number\" v-model=\"nutritionGoals.fat\" placeholder=\"输入脂肪目标\" />\r\n          <text class=\"unit\">g</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">饮食偏好</text>\r\n      </view>\r\n\r\n      <view class=\"preference-list\">\r\n        <view class=\"preference-item\" v-for=\"(item, index) in dietPreferences\" :key=\"index\" @click=\"togglePreference(index)\">\r\n          <text class=\"preference-name\">{{ item.name }}</text>\r\n          <switch :checked=\"item.selected\" color=\"#4CAF50\" @change=\"onSwitchChange($event, index)\" />\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"save-btn\" @click=\"saveGoals\">\r\n      <text>保存设置</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapActions } from 'vuex'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      calorieTarget: '',\r\n      nutritionGoals: {\r\n        protein: '',\r\n        carbs: '',\r\n        fat: ''\r\n      },\r\n      dietPreferences: []\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      userInfo: state => state.user.userInfo,\r\n      storeNutritionGoals: state => state.user.nutritionGoals,\r\n      storeDietPreferences: state => state.user.dietPreferences\r\n    })\r\n  },\r\n  onLoad() {\r\n    this.initData()\r\n  },\r\n  methods: {\r\n    ...mapActions({\r\n      updateUserInfo: 'user/updateUserInfo',\r\n      updateNutritionGoal: 'user/updateNutritionGoal',\r\n      getNutritionGoal: 'user/getNutritionGoal'\r\n    }),\r\n    async initData() {\r\n      try {\r\n        // 显示加载中\r\n        uni.showLoading({\r\n          title: '加载中...',\r\n          mask: true\r\n        })\r\n\r\n        // 先调用API获取最新数据\r\n        await this.getNutritionGoal()\r\n\r\n        // 然后用store中的最新数据更新页面\r\n        this.calorieTarget = this.userInfo.calorieTarget || 2200\r\n\r\n        // 营养目标\r\n        this.nutritionGoals = {\r\n          protein: this.storeNutritionGoals.protein || 65,\r\n          carbs: this.storeNutritionGoals.carbs || 300,\r\n          fat: this.storeNutritionGoals.fat || 70\r\n        }\r\n\r\n        // 饮食偏好\r\n        this.dietPreferences = [...this.storeDietPreferences]\r\n      } catch (error) {\r\n        console.error('初始化数据失败', error)\r\n        uni.showToast({\r\n          title: '获取数据失败，请重试',\r\n          icon: 'none'\r\n        })\r\n      } finally {\r\n        uni.hideLoading()\r\n      }\r\n    },\r\n    togglePreference(index) {\r\n      this.dietPreferences[index].selected = !this.dietPreferences[index].selected\r\n    },\r\n    onSwitchChange(e, index) {\r\n      this.dietPreferences[index].selected = e.detail.value\r\n    },\r\n    async saveGoals() {\r\n      // 验证输入\r\n      if (!this.calorieTarget) {\r\n        uni.showToast({\r\n          title: '请输入热量目标',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      try {\r\n        // 显示加载中\r\n        uni.showLoading({\r\n          title: '保存中...',\r\n          mask: true\r\n        })\r\n\r\n        // 准备数据\r\n        const data = {\r\n          calorieTarget: Number(this.calorieTarget),\r\n          nutritionGoals: {\r\n            protein: Number(this.nutritionGoals.protein),\r\n            carbs: Number(this.nutritionGoals.carbs),\r\n            fat: Number(this.nutritionGoals.fat)\r\n          },\r\n          dietPreferences: this.dietPreferences\r\n        }\r\n\r\n        // 调用 Vuex action 保存数据\r\n        await this.updateNutritionGoal(data)\r\n\r\n        uni.showToast({\r\n          title: '保存成功',\r\n          icon: 'success'\r\n        })\r\n\r\n        setTimeout(() => {\r\n          uni.navigateBack()\r\n        }, 1500)\r\n      } catch (error) {\r\n        console.error('保存目标失败', error)\r\n        uni.showToast({\r\n          title: '保存失败，请重试',\r\n          icon: 'none'\r\n        })\r\n      } finally {\r\n        uni.hideLoading()\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.goal-setting {\r\n  min-height: 100vh;\r\n  background-color: #f8f8f8;\r\n  padding: 20rpx;\r\n}\r\n\r\n.section {\r\n  background-color: #ffffff;\r\n  border-radius: 10rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n\r\n  .section-header {\r\n    margin-bottom: 20rpx;\r\n\r\n    .section-title {\r\n      font-size: 32rpx;\r\n      font-weight: bold;\r\n      color: #333;\r\n    }\r\n  }\r\n}\r\n\r\n.form-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .form-label {\r\n    width: 200rpx;\r\n    font-size: 28rpx;\r\n    color: #333;\r\n  }\r\n\r\n  .form-input {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    border-bottom: 1rpx solid #e0e0e0;\r\n    padding: 10rpx 0;\r\n\r\n    input {\r\n      flex: 1;\r\n      height: 60rpx;\r\n      font-size: 28rpx;\r\n    }\r\n\r\n    .unit {\r\n      font-size: 28rpx;\r\n      color: #999;\r\n      margin-left: 10rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.preference-list {\r\n  .preference-item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 20rpx 0;\r\n    border-bottom: 1rpx solid #f0f0f0;\r\n\r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n\r\n    .preference-name {\r\n      font-size: 28rpx;\r\n      color: #333;\r\n    }\r\n  }\r\n}\r\n\r\n.save-btn {\r\n  background-color: #4CAF50;\r\n  border-radius: 10rpx;\r\n  padding: 30rpx;\r\n  text-align: center;\r\n  margin-top: 60rpx;\r\n\r\n  text {\r\n    font-size: 32rpx;\r\n    color: #ffffff;\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goal.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goal.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751115964926\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}