{"version": 3, "sources": ["webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/components/common/NutritionCircle.vue?6c0f", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/components/common/NutritionCircle.vue?1be4", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/components/common/NutritionCircle.vue?a64d", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/components/common/NutritionCircle.vue?b903", "uni-app:///components/common/NutritionCircle.vue", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/components/common/NutritionCircle.vue?abfe", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/components/common/NutritionCircle.vue?a95d"], "names": ["name", "props", "percentage", "type", "default", "target", "current", "color", "bgColor", "label", "size", "strokeWidth", "data", "canvasId", "canvasContext", "pixelRatio", "filters", "formatValue", "computed", "formattedPercentage", "unitText", "watch", "mounted", "setTimeout", "methods", "initCanvas", "query", "fields", "node", "exec", "canvas", "ctx", "initCanvasLegacy", "drawCircle", "centerX", "gradient", "drawCircleLegacy", "redraw"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;AACc;;;AAG5E;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAA4mB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8BhoB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;EACA;EACAQ;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACAC;IACAnB;MAAA;MACA;QACA;MACA;IACA;IACAQ;MAAA;MACA;QACA;MACA;IACA;EACA;EACAY;IAAA;IACA;MACAC;QACA;MACA;IACA;EACA;;EACAC;IACA;IACAC;MAAA;MACA;MACAC,kCACAC;QAAAC;QAAAlB;MAAA,GACAmB;QACA;UACA;UACA;UACA;QACA;QAEA;QACA;QAEA;QACA;QACAC;QACAA;;QAEA;QACAC;QAEA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MACA;MACA;;MAEA;MACAF;;MAEA;MACAA;MACAA;MACAA;MACAA;;MAEA;MACAA;MACAA;MACAA;MACAA;MACAA;;MAEA;MACA;QACA;QACAA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;;QAEA;QACA;UACAA;UACAA;UACA,wCACAG,2BACAA,6CACA;UACAC;UACAA;UACAJ;UACAA;UACAA;QACA;MACA;;MAEA;MACA;QACAA;MACA;IACA;IAEA;IACAK;MACA;MAEA;MACA;MACA;MACA;;MAEA;MACAL;;MAEA;MACAA;MACAA;MACAA;MACAA;;MAEA;MACAA;MACAA;MACAA;MACAA;MACAA;;MAEA;MACA;QACA;QACAA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;MAEAA;IACA;IAEA;IACAM;MAAA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvPA;AAAA;AAAA;AAAA;AAAupC,CAAgB,wmCAAG,EAAC,C;;;;;;;;;;;ACA3qC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/common/NutritionCircle.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./NutritionCircle.vue?vue&type=template&id=33d2d982&\"\nvar renderjs\nimport script from \"./NutritionCircle.vue?vue&type=script&lang=js&\"\nexport * from \"./NutritionCircle.vue?vue&type=script&lang=js&\"\nimport style0 from \"./NutritionCircle.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/common/NutritionCircle.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./NutritionCircle.vue?vue&type=template&id=33d2d982&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var f0 = _vm._f(\"formatValue\")(_vm.current)\n  var f1 = _vm._f(\"formatValue\")(_vm.target)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        f0: f0,\n        f1: f1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./NutritionCircle.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./NutritionCircle.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"nutrition-circle\">\r\n    <view class=\"circle-container\">\r\n      <!-- 使用canvas替代SVG -->\r\n      <canvas \r\n        type=\"2d\" \r\n        class=\"circle-canvas\" \r\n        :id=\"canvasId\" \r\n        :style=\"{ width: size + 'rpx', height: size + 'rpx' }\"\r\n        @touchend=\"redraw\"\r\n      ></canvas>\r\n      <!-- 中心文字 -->\r\n      <view class=\"content\">\r\n        <view class=\"percentage\">\r\n          <text class=\"percentage-value\">{{ formattedPercentage }}</text>\r\n          <text class=\"percentage-symbol\">%</text>\r\n        </view>\r\n        <text class=\"label\">{{ label }}</text>\r\n        <view class=\"value-info\">\r\n          <text>{{ current | formatValue }}{{ unitText }}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <view class=\"target-info\">\r\n      <text>目标: {{ target | formatValue }}{{ unitText }}</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'NutritionCircle',\r\n  props: {\r\n    percentage: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    target: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    current: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    color: {\r\n      type: String,\r\n      default: '#FF7043'\r\n    },\r\n    bgColor: {\r\n      type: String,\r\n      default: 'rgba(255, 255, 255, 0.25)'\r\n    },\r\n    label: {\r\n      type: String,\r\n      default: '热量'\r\n    },\r\n    size: {\r\n      type: Number,\r\n      default: 180\r\n    },\r\n    strokeWidth: {\r\n      type: Number,\r\n      default: 10\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      canvasId: 'nutrition-circle-' + Math.floor(Math.random() * 1000),\r\n      canvasContext: null,\r\n      pixelRatio: 1\r\n    }\r\n  },\r\n  filters: {\r\n    formatValue(value) {\r\n      return Math.round(value || 0);\r\n    }\r\n  },\r\n  computed: {\r\n    formattedPercentage() {\r\n      return Math.round(Math.min(this.percentage || 0, 100));\r\n    },\r\n    unitText() {\r\n      if (this.label === '热量') return '';\r\n      return 'g';\r\n    }\r\n  },\r\n  watch: {\r\n    percentage() {\r\n      this.$nextTick(() => {\r\n        this.drawCircle();\r\n      });\r\n    },\r\n    size() {\r\n      this.$nextTick(() => {\r\n        this.initCanvas();\r\n      });\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      setTimeout(() => {\r\n        this.initCanvas();\r\n      }, 100); // 稍微延迟以确保DOM已渲染\r\n    });\r\n  },\r\n  methods: {\r\n    // 初始化canvas\r\n    initCanvas() {\r\n      const query = uni.createSelectorQuery().in(this);\r\n      query.select('#' + this.canvasId)\r\n        .fields({ node: true, size: true })\r\n        .exec(res => {\r\n          if (!res[0] || !res[0].node) {\r\n            // 如果未获取到node，尝试使用传统方式\r\n            this.initCanvasLegacy();\r\n            return;\r\n          }\r\n          \r\n          const canvas = res[0].node;\r\n          const ctx = canvas.getContext('2d');\r\n          \r\n          this.pixelRatio = uni.getSystemInfoSync().pixelRatio;\r\n          // 设置canvas实际大小\r\n          canvas.width = this.size * this.pixelRatio;\r\n          canvas.height = this.size * this.pixelRatio;\r\n          \r\n          // 缩放绘图操作，使其在高分屏上显示清晰\r\n          ctx.scale(this.pixelRatio, this.pixelRatio);\r\n          \r\n          this.canvasContext = ctx;\r\n          this.drawCircle();\r\n        });\r\n    },\r\n    \r\n    // 兼容低版本的初始化方法\r\n    initCanvasLegacy() {\r\n      const context = uni.createCanvasContext(this.canvasId, this);\r\n      this.canvasContext = context;\r\n      this.drawCircleLegacy();\r\n    },\r\n    \r\n    // 绘制圆环 - 2d标准绘制\r\n    drawCircle() {\r\n      if (!this.canvasContext) return;\r\n      \r\n      const ctx = this.canvasContext;\r\n      const centerX = this.size / 2;\r\n      const centerY = this.size / 2;\r\n      const radius = (this.size / 2) - (this.strokeWidth / 2);\r\n      \r\n      // 清空画布\r\n      ctx.clearRect(0, 0, this.size, this.size);\r\n      \r\n      // 绘制内部填充圆形\r\n      ctx.beginPath();\r\n      ctx.arc(centerX, centerY, radius - this.strokeWidth/2, 0, 2 * Math.PI);\r\n      ctx.fillStyle = 'rgba(0, 0, 0, 0.15)';\r\n      ctx.fill();\r\n      \r\n      // 绘制背景圆环\r\n      ctx.beginPath();\r\n      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);\r\n      ctx.strokeStyle = this.bgColor;\r\n      ctx.lineWidth = this.strokeWidth;\r\n      ctx.stroke();\r\n      \r\n      // 绘制进度圆环\r\n      if (this.percentage > 0) {\r\n        const progress = Math.min(this.percentage, 100) / 100;\r\n        ctx.beginPath();\r\n        // 从顶部开始绘制(-0.5π)，顺时针方向\r\n        ctx.arc(centerX, centerY, radius, -0.5 * Math.PI, (-0.5 + 2 * progress) * Math.PI);\r\n        ctx.strokeStyle = this.color;\r\n        ctx.lineCap = 'round';\r\n        ctx.lineWidth = this.strokeWidth;\r\n        ctx.stroke();\r\n        \r\n        // 添加高光效果\r\n        if (progress > 0.05) {\r\n          ctx.beginPath();\r\n          ctx.arc(centerX, centerY, radius, -0.5 * Math.PI, (-0.5 + 0.1) * Math.PI);\r\n          const gradient = ctx.createLinearGradient(\r\n            centerX, centerY - radius, \r\n            centerX, centerY - radius + this.strokeWidth\r\n          );\r\n          gradient.addColorStop(0, 'rgba(255, 255, 255, 0.7)');\r\n          gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\r\n          ctx.strokeStyle = gradient;\r\n          ctx.lineWidth = this.strokeWidth / 2;\r\n          ctx.stroke();\r\n        }\r\n      }\r\n      \r\n      // 对于非传统canvas API，需要手动更新帧\r\n      if (ctx.draw) {\r\n        ctx.draw();\r\n      }\r\n    },\r\n    \r\n    // 传统canvas绘制方法\r\n    drawCircleLegacy() {\r\n      if (!this.canvasContext) return;\r\n      \r\n      const ctx = this.canvasContext;\r\n      const centerX = this.size / 2;\r\n      const centerY = this.size / 2;\r\n      const radius = (this.size / 2) - (this.strokeWidth / 2);\r\n      \r\n      // 清空画布\r\n      ctx.clearRect(0, 0, this.size, this.size);\r\n      \r\n      // 绘制内部填充圆形\r\n      ctx.beginPath();\r\n      ctx.arc(centerX, centerY, radius - this.strokeWidth/2, 0, 2 * Math.PI);\r\n      ctx.setFillStyle('rgba(0, 0, 0, 0.15)');\r\n      ctx.fill();\r\n      \r\n      // 绘制背景圆环\r\n      ctx.beginPath();\r\n      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);\r\n      ctx.setStrokeStyle(this.bgColor);\r\n      ctx.setLineWidth(this.strokeWidth);\r\n      ctx.stroke();\r\n      \r\n      // 绘制进度圆环\r\n      if (this.percentage > 0) {\r\n        const progress = Math.min(this.percentage, 100) / 100;\r\n        ctx.beginPath();\r\n        // 从顶部开始绘制(-0.5π)，顺时针方向\r\n        ctx.arc(centerX, centerY, radius, -0.5 * Math.PI, (-0.5 + 2 * progress) * Math.PI);\r\n        ctx.setStrokeStyle(this.color);\r\n        ctx.setLineCap('round');\r\n        ctx.setLineWidth(this.strokeWidth);\r\n        ctx.stroke();\r\n      }\r\n      \r\n      ctx.draw();\r\n    },\r\n    \r\n    // 触摸结束时重绘，解决某些平台上的刷新问题\r\n    redraw() {\r\n      this.$nextTick(() => {\r\n        this.drawCircle();\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.nutrition-circle {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 10rpx;\r\n  \r\n  .circle-container {\r\n    position: relative;\r\n    width: 180rpx;\r\n    height: 180rpx;\r\n    margin-bottom: 10rpx;\r\n    \r\n    .circle-canvas {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    \r\n    .content {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      pointer-events: none; /* 允许点击穿透到canvas */\r\n      \r\n      .percentage {\r\n        display: flex;\r\n        align-items: flex-start;\r\n        \r\n        .percentage-value {\r\n          font-size: 40rpx;\r\n          font-weight: bold;\r\n          color: #ffffff;\r\n          line-height: 1;\r\n          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\r\n        }\r\n        \r\n        .percentage-symbol {\r\n          font-size: 20rpx;\r\n          color: #ffffff;\r\n          margin-top: 8rpx;\r\n          line-height: 1;\r\n          opacity: 0.9;\r\n        }\r\n      }\r\n      \r\n      .label {\r\n        font-size: 24rpx;\r\n        color: rgba(255, 255, 255, 0.95);\r\n        margin-top: 6rpx;\r\n        text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\r\n      }\r\n      \r\n      .value-info {\r\n        font-size: 20rpx;\r\n        color: rgba(255, 255, 255, 0.9);\r\n        margin-top: 6rpx;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .target-info {\r\n    font-size: 20rpx;\r\n    color: rgba(255, 255, 255, 0.9);\r\n    font-weight: 500;\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./NutritionCircle.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./NutritionCircle.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751115964940\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}