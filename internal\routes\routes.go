package routes

import (
	"log"

	"github.com/gin-gonic/gin"
	"shikeyinxiang/internal/config"
	"shikeyinxiang/internal/controllers"
	"shikeyinxiang/internal/database"
	"shikeyinxiang/internal/logic"
	"shikeyinxiang/internal/middleware"
	"shikeyinxiang/internal/repositories"
	"shikeyinxiang/internal/storage"
)

// SetupRoutes 设置所有路由
func SetupRoutes(r *gin.Engine) {
	// 应用全局中间件
	r.Use(middleware.CORSMiddleware())
	r.Use(middleware.ErrorHandler())
	r.Use(middleware.LoggerMiddleware())
	// r.Use(middleware.RequestIDMiddleware())

	// TODO: 设置404和405处理器
	//r.NoRoute(middleware.NotFoundHandler())
	//r.NoMethod(middleware.MethodNotAllowedHandler())

	// 健康检查接口
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "Service is running",
		})
	})

	// API路由组
	api := r.Group("/api")
	{
		// 认证相关路由 /api/auth
		setupAuthRoutes(api)

		// 食物管理相关路由 /api/food
		setupFoodRoutes(api)

		// 饮食记录相关路由 /api/diet-records
		setupDietRecordRoutes(api)

		// 用户管理相关路由 /api/admin/users 和 /user
		setupUserRoutes(api)

		// 营养服务相关路由 /api/nutrition, /api/health, /api/admin/nutrition
		setupNutritionRoutes(api)

		// 仪表盘相关路由 /api/admin/dashboard
		setupDashboardRoutes(api)

		// TODO: 其他路由将在后续实现
		// setupFileRoutes(api)
	}
}

// setupAuthRoutes 设置认证路由
func setupAuthRoutes(api *gin.RouterGroup) {
	// 初始化依赖
	userRepo := repositories.NewUserRepository(database.GetDB())
	authLogic := logic.NewAuth(userRepo, &config.AppConfig.Wechat)
	authController := controllers.NewAuthController(authLogic)

	auth := api.Group("/auth")
	{
		// 公开路由（无需认证）
		auth.POST("/register", authController.Register)
		auth.POST("/user/login", authController.UserLogin)
		auth.POST("/admin/login", authController.AdminLogin)
		auth.POST("/wechat-login", authController.WechatLogin)

		// 需要认证的路由
		auth.Use(middleware.AuthMiddleware()) // 对下面的路由组统一应用认证中间件
		{
			auth.POST("/logout", authController.Logout)
			auth.POST("/change-password", authController.ChangePassword)
			auth.GET("/me", authController.GetCurrentUser)
		}
	}
}

// setupFoodRoutes 设置食物管理路由
func setupFoodRoutes(api *gin.RouterGroup) {
	// 初始化依赖
	db := database.GetDB()
	foodRepo := repositories.NewFoodRepository(db)
	categoryRepo := repositories.NewFoodCategoryRepository(db)

	// 初始化文件服务相关依赖
	r2Storage, err := storage.NewR2Storage(&config.AppConfig.R2)
	if err != nil {
		log.Fatalf("Failed to initialize R2 storage: %v", err)
	}
	fileService := logic.NewFileLogic(r2Storage)

	// 初始化Logic层
	foodLogic := logic.NewFoodLogic(foodRepo, categoryRepo, fileService)
	categoryLogic := logic.NewFoodCategoryLogic(categoryRepo, foodRepo)

	// 初始化Controller
	foodController := controllers.NewFoodController(foodLogic, categoryLogic)
	adminFoodController := controllers.NewAdminFoodController(foodLogic, categoryLogic)

	// 用户端路由组 /api/food
	food := api.Group("/food")
	{
		// 公开路由（无需认证）
		food.GET("/list", foodController.GetFoodList)                              // 获取食物列表
		food.GET("/:id", foodController.GetFoodDetail)                             // 获取食物详情
		food.GET("/search", foodController.SearchFoods)                            // 搜索食物
		food.GET("/categories", foodController.GetCategories)                      // 获取所有分类
		food.GET("/category/:categoryId/foods", foodController.GetFoodsByCategory) // 根据分类获取食物
	}

	// 管理端路由组 /api/admin/food
	admin := api.Group("/admin")
	admin.Use(middleware.AuthMiddleware()) // 管理端路由需要认证
	{
		adminFood := admin.Group("/food")
		{
			// 食物管理
			adminFood.POST("", adminFoodController.CreateFood)       // 创建食物
			adminFood.PUT("/:id", adminFoodController.UpdateFood)    // 更新食物
			adminFood.DELETE("/:id", adminFoodController.DeleteFood) // 删除食物
			adminFood.GET("/list", adminFoodController.GetFoodList)  // 获取食物列表
			adminFood.GET("/:id", adminFoodController.GetFoodDetail) // 获取食物详情

			// 分类管理（与Java版本路径保持一致）
			adminFood.GET("/categories", adminFoodController.GetAllCategories)                 // 获取所有分类（与Java版本保持一致）
			adminFood.POST("/category", adminFoodController.CreateCategory)                    // 创建分类
			adminFood.PUT("/category/:id", adminFoodController.UpdateCategory)                 // 更新分类
			adminFood.DELETE("/category/:id", adminFoodController.DeleteCategory)              // 删除分类
			adminFood.GET("/category/page", adminFoodController.GetCategoryPage)               // 分页查询分类（与Java版本保持一致）
			adminFood.GET("/category/:id", adminFoodController.GetCategoryDetail)              // 获取分类详情

			// Go项目扩展的分类接口（保留有用功能）
			adminFood.PUT("/categories/:id/sort", adminFoodController.UpdateCategorySortOrder) // 更新分类排序

			// 批量操作接口（与Java版本保持一致）
			adminFood.POST("/import", adminFoodController.ImportFoods) // 批量导入食物数据

			// 图片相关接口（与Java版本保持一致）
			adminFood.GET("/upload-image-url", adminFoodController.GetUploadImageUrl) // 获取图片上传URL
			adminFood.GET("/:id/image-url", adminFoodController.GetFoodImageUrl)      // 获取食物图片下载URL
			adminFood.PUT("/:id/image", adminFoodController.UpdateFoodImageUrl)       // 更新食物图片URL
		}
	}
}

// setupDietRecordRoutes 设置饮食记录路由
func setupDietRecordRoutes(api *gin.RouterGroup) {
	// 初始化依赖
	db := database.GetDB()
	dietRecordRepo := repositories.NewDietRecordRepository(db)
	dietRecordFoodRepo := repositories.NewDietRecordFoodRepository(db)
	foodRepo := repositories.NewFoodRepository(db)

	// 初始化Logic层
	dietRecordLogic := logic.NewDietRecordLogic(dietRecordRepo, dietRecordFoodRepo, foodRepo)
	dietRecordAdminLogic := logic.NewDietRecordAdminLogic(dietRecordRepo, dietRecordFoodRepo, foodRepo)

	// 初始化Controller
	dietRecordController := controllers.NewDietRecordController(dietRecordLogic)
	dietRecordAdminController := controllers.NewDietRecordAdminController(dietRecordAdminLogic)

	// 用户端路由组 /api/diet-records
	dietRecords := api.Group("/diet-records")
	dietRecords.Use(middleware.AuthMiddleware()) // 用户端路由需要认证
	{
		// 饮食记录CRUD操作
		dietRecords.GET("", dietRecordController.GetDietRecordList)              // 获取饮食记录列表（分页）- 根路径
		dietRecords.POST("", dietRecordController.CreateDietRecord)               // 创建饮食记录
		dietRecords.GET("/:id", dietRecordController.GetDietRecord)               // 获取饮食记录详情
		dietRecords.PUT("", dietRecordController.UpdateDietRecord)                // 更新饮食记录
		dietRecords.DELETE("/:id", dietRecordController.DeleteDietRecord)         // 删除饮食记录
		dietRecords.DELETE("/batch", dietRecordController.BatchDeleteDietRecords) // 批量删除饮食记录

		// 查询和搜索
		dietRecords.GET("/search", dietRecordController.SearchDietRecords)        // 搜索饮食记录
		dietRecords.GET("/date/:date", dietRecordController.GetDietRecordsByDate) // 获取指定日期的饮食记录

		// 营养统计
		dietRecords.GET("/nutrition/stats", dietRecordController.GetNutritionStats) // 获取营养统计
	}

	// 管理端路由组 /api/admin/diet-records
	admin := api.Group("/admin")
	admin.Use(middleware.AuthMiddleware())      // 管理端路由需要认证
	admin.Use(middleware.AdminOnlyMiddleware()) // 管理端路由需要管理员权限
	{
		adminDietRecords := admin.Group("/diet-records")
		{
			// 饮食记录管理
			adminDietRecords.GET("", dietRecordAdminController.GetDietRecordList)              // 获取饮食记录列表（支持跨用户）- 根路径
			adminDietRecords.GET("/:id", dietRecordAdminController.GetDietRecord)               // 获取饮食记录详情
			adminDietRecords.DELETE("/:id", dietRecordAdminController.DeleteDietRecord)         // 删除饮食记录
			adminDietRecords.DELETE("/batch", dietRecordAdminController.BatchDeleteDietRecords) // 批量删除饮食记录

			// 营养统计和分析
			adminDietRecords.GET("/nutrition/stats", dietRecordAdminController.GetNutritionStats)                   // 获取营养统计（跨用户）
			adminDietRecords.GET("/users/:userId/nutrition/stats", dietRecordAdminController.GetUserNutritionStats) // 获取指定用户营养统计
			adminDietRecords.GET("/system/stats", dietRecordAdminController.GetSystemStats)                         // 获取系统级统计

			// 数据导出
			adminDietRecords.GET("/export", dietRecordAdminController.ExportDietRecords) // 导出饮食记录数据
		}
	}
}

// setupUserRoutes 设置用户管理路由
func setupUserRoutes(api *gin.RouterGroup) {
	// 初始化依赖
	db := database.GetDB()
	userRepo := repositories.NewUserRepository(db)

	// 初始化文件服务相关依赖
	r2Storage, err := storage.NewR2Storage(&config.AppConfig.R2)
	if err != nil {
		log.Fatalf("Failed to initialize R2 storage: %v", err)
	}
	fileService := logic.NewFileLogic(r2Storage)

	// 初始化营养目标仓储
	nutritionGoalRepo := repositories.NewUserNutritionGoalRepository(db)

	// 初始化Logic层
	userLogic := logic.NewUserLogic(userRepo, fileService)
	nutritionGoalLogic := logic.NewUserNutritionGoalLogic(userRepo, nutritionGoalRepo)

	// 初始化Controller
	userController := controllers.NewUserController(userLogic, nutritionGoalLogic)
	adminUserController := controllers.NewAdminUserController(userLogic)

	// 用户端路由组 /user
	user := api.Group("/user")
	user.Use(middleware.AuthMiddleware()) // 用户端路由需要认证
	{
		// 用户信息管理
		user.GET("/info", userController.GetUserInfo)                                    // 获取当前用户信息
		user.PUT("/update", userController.UpdateUserInfo)                               // 更新用户信息

		// 头像管理
		user.GET("/avatar", userController.GetAvatarUrl)                                 // 获取头像URL
		user.POST("/avatar/upload-url", userController.GenerateAvatarUploadUrl)          // 生成头像上传URL
		user.POST("/avatar", userController.UploadAvatar)                                // 上传头像（返回预签名URL）
		user.POST("/avatar/proxy", userController.UploadAvatarProxy)                     // 代理上传头像
		user.PUT("/avatar", userController.UpdateAvatar)                                 // 更新头像URL

		// 密码管理
		user.POST("/change-password", userController.ChangePassword)                     // 修改密码

		// 营养目标管理
		user.GET("/nutrition-goal", userController.GetNutritionGoal)                     // 获取营养目标
		user.PUT("/nutrition-goal", userController.UpdateNutritionGoal)                  // 更新营养目标
	}

	// 管理端路由组 /api/admin/users
	admin := api.Group("/admin")
	admin.Use(middleware.AuthMiddleware())      // 管理端路由需要认证
	admin.Use(middleware.AdminOnlyMiddleware()) // 管理端路由需要管理员权限
	{
		adminUsers := admin.Group("/users")
		{
			// 用户管理CRUD操作
			adminUsers.GET("", adminUserController.ListUsers)                            // 分页查询用户列表
			adminUsers.POST("", adminUserController.CreateUser)                          // 创建用户
			adminUsers.GET("/:id", adminUserController.GetUserDetail)                    // 获取用户详情
			adminUsers.PUT("/:id", adminUserController.UpdateUser)                       // 更新用户信息
			adminUsers.DELETE("/:id", adminUserController.DeleteUser)                    // 删除用户
			adminUsers.PUT("/:id/status", adminUserController.UpdateUserStatus)          // 更新用户状态
		}
	}
}

// setupNutritionRoutes 设置营养服务路由
func setupNutritionRoutes(api *gin.RouterGroup) {
	// 初始化依赖
	db := database.GetDB()

	// 初始化仓储层
	nutritionAdviceRepo := repositories.NewNutritionAdviceRepository(db)
	dietRecordRepo := repositories.NewDietRecordRepository(db)
	dietRecordFoodRepo := repositories.NewDietRecordFoodRepository(db)
	userRepo := repositories.NewUserRepository(db)
	nutritionGoalRepo := repositories.NewUserNutritionGoalRepository(db)

	// 初始化服务层
	dietRecordLogic := logic.NewDietRecordLogic(dietRecordRepo, dietRecordFoodRepo, repositories.NewFoodRepository(db))
	userLogic := logic.NewUserLogic(userRepo, nil) // 文件服务在这里不需要
	nutritionGoalLogic := logic.NewUserNutritionGoalLogic(userRepo, nutritionGoalRepo)

	// 初始化营养服务
	nutritionStatLogic := logic.NewNutritionStatLogic(dietRecordLogic, userLogic, nutritionGoalLogic)
	nutritionAdviceLogic := logic.NewNutritionAdviceLogic(nutritionAdviceRepo, nutritionStatLogic)
	healthReportLogic := logic.NewHealthReportLogic(nutritionStatLogic, nutritionAdviceLogic, userLogic, nutritionGoalLogic)

	// 初始化控制器
	nutritionController := controllers.NewNutritionController(nutritionStatLogic, nutritionAdviceLogic, healthReportLogic)
	adminNutritionController := controllers.NewAdminNutritionController(nutritionStatLogic, nutritionAdviceLogic)

	// 用户端营养路由组 /api/nutrition
	nutrition := api.Group("/nutrition")
	nutrition.Use(middleware.AuthMiddleware()) // 营养路由需要认证
	{
		nutrition.GET("/daily", nutritionController.GetDailyNutrition)       // 获取每日营养统计
		nutrition.GET("/trend", nutritionController.GetNutritionTrend)       // 获取营养趋势
		nutrition.GET("/details", nutritionController.GetNutritionDetails)   // 获取营养详情
		nutrition.GET("/advice", nutritionController.GetNutritionAdvice)     // 获取营养建议
	}

	// 健康报告路由组 /api/health
	health := api.Group("/health")
	health.Use(middleware.AuthMiddleware()) // 健康报告路由需要认证
	{
		health.GET("/report", nutritionController.GetHealthReport) // 获取健康报告
	}

	// 管理端营养路由组 /api/admin/nutrition
	admin := api.Group("/admin")
	admin.Use(middleware.AuthMiddleware())      // 管理端路由需要认证
	admin.Use(middleware.AdminOnlyMiddleware()) // 管理端路由需要管理员权限
	{
		adminNutrition := admin.Group("/nutrition")
		{
			// 营养统计管理
			adminNutrition.GET("/trend", adminNutritionController.GetAllNutritionTrend)                    // 获取全体用户营养趋势
			adminNutrition.GET("/compliance-rate", adminNutritionController.GetNutritionComplianceRate)    // 获取营养达标率

			// 营养建议管理
			adminNutrition.GET("/advice", adminNutritionController.GetAllAdvices)                          // 获取营养建议列表
			adminNutrition.POST("/advice", adminNutritionController.CreateAdvice)                          // 创建营养建议
			adminNutrition.GET("/advice/:id", adminNutritionController.GetAdviceById)                      // 根据ID获取营养建议
			adminNutrition.PUT("/advice/:id", adminNutritionController.UpdateAdvice)                       // 更新营养建议
			adminNutrition.DELETE("/advice/:id", adminNutritionController.DeleteAdvice)                    // 删除营养建议
			adminNutrition.PUT("/advice/:id/status", adminNutritionController.UpdateAdviceStatus)          // 更新营养建议状态
			adminNutrition.GET("/advice/condition/:conditionType", adminNutritionController.GetAdvicesByConditionType) // 根据条件类型获取建议
		}
	}
}

// setupDashboardRoutes 设置仪表盘路由
func setupDashboardRoutes(api *gin.RouterGroup) {
	// 初始化依赖
	db := database.GetDB()

	// 初始化仓储层
	userRepo := repositories.NewUserRepository(db)
	dietRecordRepo := repositories.NewDietRecordRepository(db)
	dietRecordFoodRepo := repositories.NewDietRecordFoodRepository(db)
	foodRepo := repositories.NewFoodRepository(db)
	nutritionGoalRepo := repositories.NewUserNutritionGoalRepository(db)

	// 初始化文件服务相关依赖
	r2Storage, err := storage.NewR2Storage(&config.AppConfig.R2)
	if err != nil {
		log.Fatalf("Failed to initialize R2 storage: %v", err)
	}
	fileService := logic.NewFileLogic(r2Storage)

	// 初始化服务层
	userLogic := logic.NewUserLogic(userRepo, fileService)
	dietRecordLogic := logic.NewDietRecordLogic(dietRecordRepo, dietRecordFoodRepo, foodRepo)
	dietRecordAdminLogic := logic.NewDietRecordAdminLogic(dietRecordRepo, dietRecordFoodRepo, foodRepo)
	nutritionGoalLogic := logic.NewUserNutritionGoalLogic(userRepo, nutritionGoalRepo)
	nutritionStatLogic := logic.NewNutritionStatLogic(dietRecordLogic, userLogic, nutritionGoalLogic)

	// 初始化仪表盘服务
	dashboardLogic := logic.NewDashboardLogic(userLogic, dietRecordLogic, dietRecordAdminLogic, nutritionStatLogic)

	// 初始化控制器
	adminDashboardController := controllers.NewAdminDashboardController(dashboardLogic)

	// 管理端仪表盘路由组 /api/admin/dashboard
	admin := api.Group("/admin")
	admin.Use(middleware.AuthMiddleware())      // 管理端路由需要认证
	admin.Use(middleware.AdminOnlyMiddleware()) // 管理端路由需要管理员权限
	{
		dashboard := admin.Group("/dashboard")
		{
			// 仪表盘统计数据
			dashboard.GET("/stats", adminDashboardController.GetDashboardStats)

			// 营养趋势数据
			dashboard.GET("/nutrition-trend", adminDashboardController.GetNutritionTrend)

			// 最新饮食记录
			dashboard.GET("/latest-diet-records", adminDashboardController.GetLatestDietRecords)

			// 饮食记录详情
			dashboard.GET("/diet-record/:recordId", adminDashboardController.GetDietRecordDetail)

			// 热门食物统计
			dashboard.GET("/popular-foods", adminDashboardController.GetPopularFoods)
		}
	}
}
