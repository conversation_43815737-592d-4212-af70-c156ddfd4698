{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/index.vue?68dd", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/index.vue?20a7", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/index.vue?d4a4", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/index.vue?c7cb", "uni-app:///pages/mine/index.vue", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/index.vue?50b8", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/index.vue?3010"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "computed", "userInfo", "is<PERSON>ogin", "avat<PERSON><PERSON><PERSON>", "onLoad", "onShow", "methods", "getUserInfo", "logoutAction", "navigateTo", "uni", "url", "loadAvatarUrl", "res", "avatar", "console", "showFeedback", "title", "content", "showCancel", "showAbout", "logout", "success", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACoFtnB;AACA;AAAA;AAAA;AAAA,eAEA;EACAC,0CACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;IACA;IACAC;MACA;IACA;EAAA,EACA;EACAC;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;EACA;EACAC,yCACA;IACAC;IACAC;EACA;IACAC;MACAC;QACAC;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBACA,0EACA;oBACAC;kBAAA,GACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MACAN;QACAO;QACAC;QACAC;MACA;IACA;IACAC;MACAV;QACAO;QACAC;QACAC;MACA;IACA;IACAE;MAAA;MACAX;QACAO;QACAC;QACAI;UACA;YACA;YACAZ;cACAO;cACAM;YACA;UACA;QACA;MACA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACtKA;AAAA;AAAA;AAAA;AAA6oC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAjqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4bd6864f&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4bd6864f&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"mine\">\r\n    <!-- 顶部安全区域 -->\r\n    <view class=\"safe-area\"></view>\r\n\r\n    <!-- 顶部用户信息 -->\r\n    <view class=\"user-info\">\r\n      <view class=\"avatar-container\" @click=\"navigateTo('/pages/mine/profile')\">\r\n        <image class=\"avatar\" :src=\"avatarPath\"></image>\r\n      </view>\r\n      <view class=\"user-detail\">\r\n        <text class=\"nickname\">{{ userInfo.username || '未登录' }}</text>\r\n      </view>\r\n      <view class=\"edit-btn\" @click=\"navigateTo('/pages/mine/profile')\">\r\n        <image src=\"/static/icons/edit.png\"></image>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 功能菜单 -->\r\n    <view class=\"menu-list\">\r\n      <view class=\"menu-section\">\r\n        <view class=\"menu-item\" @click=\"navigateTo('/pages/mine/goal')\">\r\n          <view class=\"menu-icon\">\r\n            <image src=\"/static/icons/goal.png\"></image>\r\n          </view>\r\n          <view class=\"menu-content\">\r\n            <text class=\"menu-title\">饮食目标</text>\r\n            <text class=\"menu-desc\">设置每日营养摄入目标</text>\r\n          </view>\r\n          <view class=\"menu-arrow\">\r\n            <image src=\"/static/icons/arrow-right.png\"></image>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"menu-item\" @click=\"navigateTo('/pages/mine/settings')\">\r\n          <view class=\"menu-icon\">\r\n            <image src=\"/static/icons/settings.png\"></image>\r\n          </view>\r\n          <view class=\"menu-content\">\r\n            <text class=\"menu-title\">设置</text>\r\n            <text class=\"menu-desc\">通知、隐私等设置</text>\r\n          </view>\r\n          <view class=\"menu-arrow\">\r\n            <image src=\"/static/icons/arrow-right.png\"></image>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"menu-section\">\r\n        <view class=\"menu-item\" @click=\"showFeedback()\">\r\n          <view class=\"menu-icon\">\r\n            <image src=\"/static/icons/feedback.png\"></image>\r\n          </view>\r\n          <view class=\"menu-content\">\r\n            <text class=\"menu-title\">意见反馈</text>\r\n            <text class=\"menu-desc\">帮助我们改进产品</text>\r\n          </view>\r\n          <view class=\"menu-arrow\">\r\n            <image src=\"/static/icons/arrow-right.png\"></image>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"menu-item\" @click=\"showAbout()\">\r\n          <view class=\"menu-icon\">\r\n            <image src=\"/static/icons/about.png\"></image>\r\n          </view>\r\n          <view class=\"menu-content\">\r\n            <text class=\"menu-title\">关于我们</text>\r\n            <text class=\"menu-desc\">了解更多信息</text>\r\n          </view>\r\n          <view class=\"menu-arrow\">\r\n            <image src=\"/static/icons/arrow-right.png\"></image>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"logout-btn\" @click=\"logout\" v-if=\"isLogin\">\r\n        <text>退出登录</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapActions } from 'vuex'\r\nimport { getUserAvatar } from '@/api/user'\r\n\r\nexport default {\r\n  computed: {\r\n    ...mapState({\r\n      userInfo: state => state.user.userInfo,\r\n      isLogin: state => state.user.isLogin\r\n    }),\r\n    // 简化的头像路径计算：直接使用远程URL或默认头像\r\n    avatarPath() {\r\n      return this.userInfo.avatar || '/static/images/default-avatar.png'\r\n    }\r\n  },\r\n  onLoad() {\r\n    // 如果已登录但没有头像URL，异步获取\r\n    if (this.isLogin && !this.userInfo.avatar) {\r\n      this.loadAvatarUrl()\r\n    }\r\n  },\r\n  onShow() {\r\n    // 页面显示时，如果已登录但没有头像URL，异步获取\r\n    if (this.isLogin && !this.userInfo.avatar) {\r\n      this.loadAvatarUrl()\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions({\r\n      getUserInfo: 'user/getUserInfo',\r\n      logoutAction: 'user/logout'\r\n    }),\r\n    navigateTo(url) {\r\n      uni.navigateTo({\r\n        url\r\n      })\r\n    },\r\n    // 简化的头像URL加载方法\r\n    async loadAvatarUrl() {\r\n      try {\r\n        const res = await getUserAvatar()\r\n        if (res.data && res.data.avatarUrl) {\r\n          // 更新头像URL到store\r\n          this.$store.commit('user/SET_USER_INFO', {\r\n            ...this.userInfo,\r\n            avatar: res.data.avatarUrl\r\n          })\r\n        }\r\n      } catch (error) {\r\n        console.log('获取头像URL失败', error)\r\n        // 静默失败，使用默认头像\r\n      }\r\n    },\r\n    showFeedback() {\r\n      uni.showModal({\r\n        title: '意见反馈',\r\n        content: '感谢您的反馈，我们将不断改进产品！',\r\n        showCancel: false\r\n      })\r\n    },\r\n    showAbout() {\r\n      uni.showModal({\r\n        title: '关于我们',\r\n        content: '饮食记录小程序 v1.0.0\\n帮助您记录饮食，改善健康',\r\n        showCancel: false\r\n      })\r\n    },\r\n    logout() {\r\n      uni.showModal({\r\n        title: '提示',\r\n        content: '确定要退出登录吗？',\r\n        success: res => {\r\n          if (res.confirm) {\r\n            this.logoutAction()\r\n            uni.showToast({\r\n              title: '已退出登录',\r\n              icon: 'success'\r\n            })\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.mine {\r\n  min-height: 100vh;\r\n  background-color: #f8f8f8;\r\n}\r\n\r\n.safe-area {\r\n  height: 80rpx; /* 与其他页面保持一致的顶部安全区域高度 */\r\n  background-color: #4CAF50;\r\n}\r\n\r\n.user-info {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx 30rpx 30rpx;\r\n  background-color: #4CAF50;\r\n\r\n  .avatar-container {\r\n    margin-right: 25rpx;\r\n\r\n    .avatar {\r\n      width: 110rpx;\r\n      height: 110rpx;\r\n      border-radius: 55rpx;\r\n      border: 4rpx solid #ffffff;\r\n    }\r\n  }\r\n\r\n  .user-detail {\r\n    flex: 1;\r\n\r\n    .nickname {\r\n      font-size: 34rpx;\r\n      font-weight: bold;\r\n      color: #ffffff;\r\n      margin-bottom: 15rpx;\r\n    }\r\n\r\n    .user-stats {\r\n      display: flex;\r\n\r\n      .stat-item {\r\n        margin-right: 25rpx;\r\n\r\n        .stat-value {\r\n          font-size: 30rpx;\r\n          color: #ffffff;\r\n          font-weight: bold;\r\n          display: block;\r\n        }\r\n\r\n        .stat-label {\r\n          font-size: 22rpx;\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .edit-btn {\r\n    width: 60rpx;\r\n    height: 60rpx;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n\r\n    image {\r\n      width: 36rpx;\r\n      height: 36rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.menu-list {\r\n  padding: 20rpx;\r\n\r\n  .menu-section {\r\n    background-color: #ffffff;\r\n    border-radius: 10rpx;\r\n    margin-bottom: 20rpx;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .menu-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 30rpx;\r\n    border-bottom: 1rpx solid #f0f0f0;\r\n\r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n\r\n    .menu-icon {\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin-right: 20rpx;\r\n\r\n      image {\r\n        width: 40rpx;\r\n        height: 40rpx;\r\n      }\r\n    }\r\n\r\n    .menu-content {\r\n      flex: 1;\r\n\r\n      .menu-title {\r\n        font-size: 30rpx;\r\n        color: #333;\r\n        margin-bottom: 6rpx;\r\n        display: block;\r\n      }\r\n\r\n      .menu-desc {\r\n        font-size: 24rpx;\r\n        color: #999;\r\n      }\r\n    }\r\n\r\n    .menu-arrow {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n\r\n      image {\r\n        width: 24rpx;\r\n        height: 24rpx;\r\n      }\r\n    }\r\n  }\r\n\r\n  .logout-btn {\r\n    margin-top: 60rpx;\r\n    background-color: #ffffff;\r\n    border-radius: 10rpx;\r\n    padding: 30rpx;\r\n    text-align: center;\r\n\r\n    text {\r\n      font-size: 32rpx;\r\n      color: #FF5252;\r\n    }\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751115964921\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}