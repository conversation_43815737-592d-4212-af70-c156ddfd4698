{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/App.vue?fc2a", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/App.vue?a7e5", "uni-app:///App.vue", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/App.vue?d0b5", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/App.vue?5dfd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "config", "productionTip", "<PERSON><PERSON><PERSON><PERSON>", "err", "vm", "info", "console", "error", "systemInfo", "uni", "getSystemInfoSync", "prototype", "$systemInfo", "mixin", "methods", "navigateTo", "url", "navigateBack", "delta", "switchTab", "showToast", "title", "icon", "App", "mpType", "app", "store", "$mount", "whiteList", "validateToken", "isTokenExpired", "removeStorageSync", "commit", "addInterceptor", "invoke", "params", "split", "some", "page", "includes", "data", "lastCheckTime", "onLaunch", "onShow", "onHide", "getUserInfo", "checkLoginStatus", "checkLoginAndRedirect"], "mappings": ";;;;;;;;;;;;;;AAAA;AAGA;AACA;AACA;AACA;AAAgD;AAAA;AALhD;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB,CAAC;;AAM3DC,YAAG,CAACC,MAAM,CAACC,aAAa,GAAG,KAAK;;AAEhC;AACAF,YAAG,CAACC,MAAM,CAACE,YAAY,GAAG,UAASC,GAAG,EAAEC,EAAE,EAAEC,IAAI,EAAE;EAChDC,OAAO,CAACC,KAAK,CAAC,QAAQ,EAAEJ,GAAG,CAAC;EAC5BG,OAAO,CAACD,IAAI,CAAC,OAAO,EAAEA,IAAI,CAAC;AAC7B,CAAC;;AAED;AACA,IAAMG,UAAU,GAAGC,GAAG,CAACC,iBAAiB,GAAGD,GAAG,CAACC,iBAAiB,EAAE,GAAG,CAAC,CAAC;AACvEX,YAAG,CAACY,SAAS,CAACC,WAAW,GAAGJ,UAAU;;AAEtC;AACAT,YAAG,CAACc,KAAK,CAAC;EACRC,OAAO,EAAE;IACPC,UAAU,sBAACC,GAAG,EAAE;MACdP,GAAG,CAACM,UAAU,CAAC;QACbC,GAAG,EAAHA;MACF,CAAC,CAAC;IACJ,CAAC;IACDC,YAAY,0BAAY;MAAA,IAAXC,KAAK,uEAAG,CAAC;MACpBT,GAAG,CAACQ,YAAY,CAAC;QACfC,KAAK,EAALA;MACF,CAAC,CAAC;IACJ,CAAC;IACDC,SAAS,qBAACH,GAAG,EAAE;MACbP,GAAG,CAACU,SAAS,CAAC;QACZH,GAAG,EAAHA;MACF,CAAC,CAAC;IACJ,CAAC;IACDI,SAAS,qBAACC,KAAK,EAAiB;MAAA,IAAfC,IAAI,uEAAG,MAAM;MAC5Bb,GAAG,CAACW,SAAS,CAAC;QACZC,KAAK,EAALA,KAAK;QACLC,IAAI,EAAJA;MACF,CAAC,CAAC;IACJ;EACF;AACF,CAAC,CAAC;AAEFC,YAAG,CAACC,MAAM,GAAG,KAAK;AAElB,IAAMC,GAAG,GAAG,IAAI1B,YAAG;EACjB2B,KAAK,EAALA;AAAK,GACFH,YAAG,EACN;AACF,UAAAE,GAAG,EAACE,MAAM,EAAE;;AAEZ;AACA,IAAMC,SAAS,GAAG,CAChB,mBAAmB,EACnB,sBAAsB,EACtB,6BAA6B,CAC9B;;AAED;AACA,SAASC,aAAa,GAAG;EACvB,IAAI,IAAAC,uBAAc,GAAE,EAAE;IACpBrB,GAAG,CAACsB,iBAAiB,CAAC,OAAO,CAAC;IAC9BtB,GAAG,CAACsB,iBAAiB,CAAC,UAAU,CAAC;IACjCtB,GAAG,CAACsB,iBAAiB,CAAC,eAAe,CAAC;IACtCtB,GAAG,CAACsB,iBAAiB,CAAC,gBAAgB,CAAC;IACvCL,cAAK,CAACM,MAAM,CAAC,uBAAuB,EAAE,KAAK,CAAC;IAC5C,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;;AAEA;AACAvB,GAAG,CAACwB,cAAc,CAAC,YAAY,EAAE;EAC/BC,MAAM,kBAACC,MAAM,EAAE;IACb;IACA,IAAMnB,GAAG,GAAGmB,MAAM,CAACnB,GAAG,CAACoB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEpC;IACA,IAAI,CAACR,SAAS,CAACS,IAAI,CAAC,UAAAC,IAAI;MAAA,OAAItB,GAAG,CAACuB,QAAQ,CAACD,IAAI,CAAC;IAAA,EAAC,EAAE;MAC/C;MACA,IAAI,CAACT,aAAa,EAAE,EAAE;QACpB;QACApB,GAAG,CAACM,UAAU,CAAC;UACbC,GAAG,EAAE;QACP,CAAC,CAAC;QACF,OAAO,KAAK;MACd;IACF;IACA,OAAOmB,MAAM;EACf;AACF,CAAC,CAAC;;AAEF;AACA1B,GAAG,CAACwB,cAAc,CAAC,WAAW,EAAE;EAC9BC,MAAM,kBAACC,MAAM,EAAE;IACb;IACA,IAAI,CAACN,aAAa,EAAE,EAAE;MACpBpB,GAAG,CAACM,UAAU,CAAC;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;MACF,OAAO,KAAK;IACd;IACA,OAAOmB,MAAM;EACf;AACF,CAAC,CAAC;;AAEF;AACA1B,GAAG,CAACwB,cAAc,CAAC,UAAU,EAAE;EAC7BC,MAAM,kBAACC,MAAM,EAAE;IACb,IAAMnB,GAAG,GAAGmB,MAAM,CAACnB,GAAG,CAACoB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI,CAACR,SAAS,CAACS,IAAI,CAAC,UAAAC,IAAI;MAAA,OAAItB,GAAG,CAACuB,QAAQ,CAACD,IAAI,CAAC;IAAA,EAAC,EAAE;MAC/C;MACA,IAAI,CAACT,aAAa,EAAE,EAAE;QACpBpB,GAAG,CAACM,UAAU,CAAC;UACbC,GAAG,EAAE;QACP,CAAC,CAAC;QACF,OAAO,KAAK;MACd;IACF;IACA,OAAOmB,MAAM;EACf;AACF,CAAC,CAAC;;AAEF;AACA1B,GAAG,CAACwB,cAAc,CAAC,YAAY,EAAE;EAC/BC,MAAM,kBAACC,MAAM,EAAE;IACb,IAAMnB,GAAG,GAAGmB,MAAM,CAACnB,GAAG,CAACoB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI,CAACR,SAAS,CAACS,IAAI,CAAC,UAAAC,IAAI;MAAA,OAAItB,GAAG,CAACuB,QAAQ,CAACD,IAAI,CAAC;IAAA,EAAC,EAAE;MAC/C;MACA,IAAI,CAACT,aAAa,EAAE,EAAE;QACpBpB,GAAG,CAACM,UAAU,CAAC;UACbC,GAAG,EAAE;QACP,CAAC,CAAC;QACF,OAAO,KAAK;MACd;IACF;IACA,OAAOmB,MAAM;EACf;AACF,CAAC,CAAC,C;;;;;;;;;;;;;AC9IF;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AAC6J;AAC7J,gBAAgB,6KAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAkkB,CAAgB,6mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACCtlB;AACA;AAAA;AAAA;AAAA,eAEA;EACAK;IACA;MACAC;IACA;EACA;;EACAC;IACApC;IACA;IACA;IACA;EACA;EACAqC;IACArC;IACA;IACA;IACA;MACAA;MACA;IACA;;IAEA;IACA;IACA;EACA;EACAsC;IACAtC;EACA;EACAQ,yCACA;IACA+B;IACAC;EACA;IACAC;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;QAAA;MAAA;QACA;MACA;;MAEA;MACA;MACA;QACA;QACAtC;UACAO;QACA;QACA;MACA;;MAEA;MACA;QACA;QACAP;QACAA;QACAA;QACAA;;QAEA;QACA;;QAEA;QACAA;UACAO;QACA;QACA;MACA;;MAEA;MACA;QACAV;MACA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACnFA;AAAA;AAAA;AAAA;AAAqlC,CAAgB,4lCAAG,EAAC,C;;;;;;;;;;;ACAzmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;// main.js: Application main entry point\nimport Vue from 'vue'\nimport App from './App'\nimport store from './store'\nimport { isTokenExpired } from './utils/request'\n\nVue.config.productionTip = false\n\n// 添加全局错误处理\nVue.config.errorHandler = function(err, vm, info) {\n  console.error('Vue错误:', err)\n  console.info('错误信息:', info)\n}\n\n// 兼容处理\nconst systemInfo = uni.getSystemInfoSync ? uni.getSystemInfoSync() : {}\nVue.prototype.$systemInfo = systemInfo\n\n// 全局混入\nVue.mixin({\n  methods: {\n    navigateTo(url) {\n      uni.navigateTo({\n        url\n      })\n    },\n    navigateBack(delta = 1) {\n      uni.navigateBack({\n        delta\n      })\n    },\n    switchTab(url) {\n      uni.switchTab({\n        url\n      })\n    },\n    showToast(title, icon = 'none') {\n      uni.showToast({\n        title,\n        icon\n      })\n    }\n  }\n})\n\nApp.mpType = 'app'\n\nconst app = new Vue({\n  store,\n  ...App\n})\napp.$mount()\n\n// 无需登录即可访问的白名单页面\nconst whiteList = [\n  'pages/login/index',\n  'pages/register/index',\n  'pages/forget-password/index'\n]\n\n// 验证token函数\nfunction validateToken() {\n  if (isTokenExpired()) {\n    uni.removeStorageSync('token')\n    uni.removeStorageSync('userInfo')\n    uni.removeStorageSync('nutritionGoal')\n    uni.removeStorageSync('lastVerifyTime')\n    store.commit('user/SET_LOGIN_STATUS', false)\n    return false\n  }\n  return true\n}\n\n// 页面跳转拦截\nuni.addInterceptor('navigateTo', {\n  invoke(params) {\n    // 获取要跳转的页面路径（去掉 ? 后面的参数）\n    const url = params.url.split('?')[0]\n    \n    // 检查该页面是否在白名单中\n    if (!whiteList.some(page => url.includes(page))) {\n      // 验证token\n      if (!validateToken()) {\n        // token不存在或已过期，跳转到登录页\n        uni.navigateTo({\n          url: '/pages/login/index'\n        })\n        return false\n      }\n    }\n    return params\n  }\n})\n\n// 拦截 switchTab\nuni.addInterceptor('switchTab', {\n  invoke(params) {\n    // 验证token\n    if (!validateToken()) {\n      uni.navigateTo({\n        url: '/pages/login/index'\n      })\n      return false\n    }\n    return params\n  }\n})\n\n// 拦截 reLaunch\nuni.addInterceptor('reLaunch', {\n  invoke(params) {\n    const url = params.url.split('?')[0]\n    if (!whiteList.some(page => url.includes(page))) {\n      // 验证token\n      if (!validateToken()) {\n        uni.navigateTo({\n          url: '/pages/login/index'\n        })\n        return false\n      }\n    }\n    return params\n  }\n})\n\n// 拦截 redirectTo\nuni.addInterceptor('redirectTo', {\n  invoke(params) {\n    const url = params.url.split('?')[0]\n    if (!whiteList.some(page => url.includes(page))) {\n      // 验证token\n      if (!validateToken()) {\n        uni.navigateTo({\n          url: '/pages/login/index'\n        })\n        return false\n      }\n    }\n    return params\n  }\n})", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\nimport { mapActions } from 'vuex'\r\nimport { isTokenExpired } from '@/utils/request'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tlastCheckTime: 0 // 记录上次检查时间\r\n\t\t}\r\n\t},\r\n\tonLaunch: function() {\r\n\t\tconsole.log('App Launch')\r\n\t\t// 记录检查时间并执行\r\n\t\tthis.lastCheckTime = Date.now()\r\n\t\tthis.checkLoginAndRedirect()\r\n\t},\r\n\tonShow: function() {\r\n\t\tconsole.log('App Show')\r\n\t\t// 判断距离上次检查时间，如果小于2秒则不执行\r\n\t\tconst now = Date.now()\r\n\t\tif (now - this.lastCheckTime < 2000) {\r\n\t\t\tconsole.log('短时间内重复进入应用，跳过登录检查')\r\n\t\t\treturn\r\n\t\t}\r\n\t\t\r\n\t\t// 记录检查时间并执行\r\n\t\tthis.lastCheckTime = now\r\n\t\tthis.checkLoginAndRedirect()\r\n\t},\r\n\tonHide: function() {\r\n\t\tconsole.log('App Hide')\r\n\t},\r\n\tmethods: {\r\n\t\t...mapActions({\r\n\t\t\tgetUserInfo: 'user/getUserInfo',\r\n\t\t\tcheckLoginStatus: 'user/checkLoginStatus'\r\n\t\t}),\r\n\t\tcheckLoginAndRedirect() {\r\n\t\t\t// 获取当前页面路径\r\n\t\t\tconst pages = getCurrentPages()\r\n\t\t\tconst currentPage = pages[pages.length - 1]\r\n\t\t\tconst currentPath = currentPage ? currentPage.route : ''\r\n\t\t\t\r\n\t\t\t// 白名单检查\r\n\t\t\tconst whiteList = ['pages/login/index', 'pages/register/index', 'pages/forget-password/index']\r\n\t\t\tif (whiteList.some(page => currentPath.includes(page))) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 判断token是否存在并检查是否过期\r\n\t\t\tconst token = uni.getStorageSync('token')\r\n\t\t\tif (!token) {\r\n\t\t\t\t// 未登录，直接跳转\r\n\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\turl: '/pages/login/index'\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 使用统一的token验证函数检查token是否有效\r\n\t\t\tif (isTokenExpired()) {\r\n\t\t\t\t// token已过期，清除存储\r\n\t\t\t\tuni.removeStorageSync('token')\r\n\t\t\t\tuni.removeStorageSync('userInfo')\r\n\t\t\t\tuni.removeStorageSync('nutritionGoal')\r\n\t\t\t\tuni.removeStorageSync('lastVerifyTime')\r\n\t\t\t\t\r\n\t\t\t\t// 更新状态\r\n\t\t\t\tthis.$store.commit('user/SET_LOGIN_STATUS', false)\r\n\t\t\t\t\r\n\t\t\t\t// 跳转到登录页\r\n\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\turl: '/pages/login/index'\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 如果token有效，可以异步获取用户信息\r\n\t\t\tthis.getUserInfo().catch(error => {\r\n\t\t\t\tconsole.error('获取用户信息失败', error)\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/* 全局样式 */\r\npage {\r\n\tfont-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;\r\n\tfont-size: 28rpx;\r\n\tline-height: 1.5;\r\n\tcolor: #333;\r\n\tbackground-color: #f8f8f8;\r\n\t/* 安全区域自适应 */\r\n\tpadding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */\r\n\tpadding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */\r\n}\r\n\r\n/* 胶囊按钮区域样式 */\r\n.status-bar {\r\n\theight: var(--status-bar-height, 20px);\r\n\twidth: 100%;\r\n}\r\n\r\n.capsule-area {\r\n\theight: 44px;\r\n\twidth: 100%;\r\n\tposition: relative;\r\n}\r\n\r\n/* 底部安全区域适配 */\r\n.safe-bottom {\r\n\tpadding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */\r\n\tpadding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */\r\n}\r\n\r\n/* 顶部安全区域适配 */\r\n.safe-top {\r\n\tpadding-top: constant(safe-area-inset-top); /* iOS 11.0 */\r\n\tpadding-top: env(safe-area-inset-top); /* iOS 11.2+ */\r\n}\r\n\r\n/* 清除默认样式 */\r\nview, text, navigator, input, textarea, button {\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n/* 通用样式 */\r\n.container {\r\n\tpadding: 20rpx;\r\n}\r\n\r\n.flex-row {\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n}\r\n\r\n.flex-column {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.flex-center {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n\r\n.flex-between {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n}\r\n\r\n.flex-around {\r\n\tdisplay: flex;\r\n\tjustify-content: space-around;\r\n\talign-items: center;\r\n}\r\n\r\n.flex-1 {\r\n\tflex: 1;\r\n}\r\n\r\n/* 颜色 */\r\n.primary-color {\r\n\tcolor: #4CAF50;\r\n}\r\n\r\n.primary-bg {\r\n\tbackground-color: #4CAF50;\r\n}\r\n\r\n.text-white {\r\n\tcolor: #ffffff;\r\n}\r\n\r\n.text-gray {\r\n\tcolor: #999999;\r\n}\r\n\r\n/* 边距 */\r\n.mt-10 {\r\n\tmargin-top: 10rpx;\r\n}\r\n\r\n.mt-20 {\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n.mb-10 {\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.mb-20 {\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.ml-10 {\r\n\tmargin-left: 10rpx;\r\n}\r\n\r\n.mr-10 {\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.p-20 {\r\n\tpadding: 20rpx;\r\n}\r\n\r\n/* 卡片样式 */\r\n.card {\r\n\tbackground-color: #ffffff;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n/* 按钮样式 */\r\n.btn {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tpadding: 20rpx 30rpx;\r\n\tborder-radius: 10rpx;\r\n\tfont-size: 30rpx;\r\n}\r\n\r\n.btn-primary {\r\n\tbackground-color: #4CAF50;\r\n\tcolor: #ffffff;\r\n}\r\n\r\n.btn-outline {\r\n\tborder: 1rpx solid #4CAF50;\r\n\tcolor: #4CAF50;\r\n}\r\n</style>\r\n", "import mod from \"-!../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751115964896\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}