{"version": 3, "sources": ["webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue?89cb", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue?fc12", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue?6865", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue?6a70", "uni-app:///uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue?c635", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue?f973"], "names": ["args", "origin", "formatterAssign", "month", "strDate", "clearTimeout", "timer", "fn", "name", "mixins", "props", "type", "default", "canvasId", "canvas2d", "background", "animation", "chartData", "categories", "series", "opts", "eopts", "loadingType", "errorShow", "errorReload", "errorMessage", "inScrollView", "reshow", "reload", "disableScroll", "optsWatch", "onzoom", "ontap", "ontouch", "onmouse", "on<PERSON><PERSON><PERSON>", "echartsH5", "echartsApp", "tooltipShow", "tooltipFormat", "tooltipCustom", "startDate", "endDate", "textEnum", "groupEnum", "pageScrollTop", "directory", "tapLegend", "menus", "data", "cid", "inWx", "inAli", "inTt", "inBd", "inH5", "inApp", "inWin", "type2d", "disScroll", "openmouse", "pixel", "c<PERSON><PERSON><PERSON>", "cHeight", "showchart", "echarts", "echartsResize", "state", "uchartsOpts", "echartsOpts", "drawData", "lastDrawTime", "created", "id", "mounted", "uni", "debounce", "_this", "destroyed", "watch", "chartDataProps", "handler", "immediate", "deep", "localdata", "optsProps", "eoptsProps", "setTimeout", "mixinDatacomErrorMessage", "params", "msg", "console", "computed", "methods", "beforeInit", "localdataInit", "resdata", "needCategories", "tmpcategories", "idate", "temp<PERSON>", "tmpData", "tmpseries", "temps<PERSON>", "seriesdata", "reloading", "checkData", "cfe", "cfu", "resize<PERSON><PERSON>ler", "createSelectorQuery", "in", "select", "boundingClientRect", "exec", "getCloudData", "then", "catch", "onMixinDatacomPropsChange", "_clear<PERSON><PERSON>", "ctx", "init", "query", "fields", "node", "size", "canvas", "saveImage", "success", "filePath", "title", "duration", "getImage", "base64", "_new<PERSON><PERSON>", "complete", "scrollLeft", "scrollRight", "_updataUChart", "_tooltipDefault", "_showTooltip", "offset", "x", "y", "index", "textList", "formatter", "_tap", "e", "currentIndex", "legendIndex", "event", "_touchStart", "lastMoveTime", "_touchMove", "_touchEnd", "_error", "emitMsg", "getRenderType", "toJSON"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmT;AACnT;AACoE;AACL;AACqC;;;AAGpG;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,iRAAM;AACR,EAAE,0RAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qRAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA2oB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC8J/pB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA;EAAA;EAAA;IAAAA;EAAA;EACA;IACA;MACA;QACAC;MACA;IACA;EACA;EACA;AACA;AAEA;EACA;IACA;MACAC;IACA;MACAF;IACA;EACA;EACA;AACA;;AAEA;AACA;EACA;EACA;EACA;EACA;EACA;IACAG;EACA;EACA;IACAC;EACA;EACA;EACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA;EACA;IAAA;MAAA;IACAC;IACAC;IACAA;MACAA;MACAC;IACA;EACA;AACA;AAAA,gBAEA;EACAC;EACAC;EACAC;IACAC;MACAA;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;QACA;UACAM;UACAC;QACA;MACA;IACA;IACAC;MACAT;MACAC;QACA;MACA;IACA;IACAS;MACAV;MACAC;QACA;MACA;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;IACA;IACAc;MACAf;MACAC;IACA;IACAe;MACAhB;MACAC;IACA;IACAgB;MACAjB;MACAC;IACA;IACAiB;MACAlB;MACAC;IACA;IACAkB;MACAnB;MACAC;IACA;IACAmB;MACApB;MACAC;IACA;IACAoB;MACArB;MACAC;IACA;IACAqB;MACAtB;MACAC;IACA;IACAsB;MACAvB;MACAC;IACA;IACAuB;MACAxB;MACAC;IACA;IACAwB;MACAzB;MACAC;IACA;IACAyB;MACA1B;MACAC;IACA;IACA0B;MACA3B;MACAC;IACA;IACA2B;MACA5B;MACAC;IACA;IACA4B;MACA7B;MACAC;IACA;IACA6B;MACA9B;MACAC;IACA;IACA8B;MACA/B;MACAC;IACA;IACA+B;MACAhC;MACAC;QACA;MACA;IACA;IACAgC;MACAjC;MACAC;QACA;MACA;IACA;IACAiC;MACAlC;MACAC;IACA;IACAkC;MACAnC;MACAC;IACA;IACAmC;MACApC;MACAC;IACA;IACAoC;MACArC;MACAC;QACA;MACA;IACA;EACA;EACAqC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;IACA;IACA;IACA;MACA;IACA;IAEA;IACA;MACA;IACA;MACA;MACA;IACA;;IAEA;;IAiBA;EACA;EACAC;IAAA;IAsBA;MACA;IACA;IAEA;IACA;IACAC,mBACAC;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;QACAC;MACA;QACAA;MACA;IACA,SACA;EAEA;EACAC;IACA;MACA;MACA;IACA;MACA;MACA;IACA;IAEAH;EAEA;EACAI;IACAC;MACAC;QACA;UACA;YACA;YACA;cACA;YACA;cACA;cACA;cACA;YACA;UACA;QACA;UACA;UACA;UACA;UACA;QACA;MACA;MACAC;MACAC;IACA;IACAC;MACAH;QACA;UACA;YACA;UACA;YACA;YACA;YACA;YACA;UACA;QACA;MACA;MACAC;MACAC;IACA;IACAE;MACAJ;QACA;UACA;YACA;UACA;QACA;UACA;UACA;UACA;UACA;QACA;MACA;MACAC;MACAC;IACA;IACAG;MACAL;QACA;UACA;YACA;UACA;QACA;UACA;UACA;UACA;QACA;MACA;MACAC;MACAC;IACA;IACAxD;MAAA;MACA;QACA4D;UACA;UACA;UACA;QACA;MACA;IACA;IACA3D;MACA;QACA;QACA;QACA;MACA;IACA;IACA4D;MACA;QACA;UAAAhF;UAAAiF;YAAA9E;YAAAY;YAAAmE;YAAAjB;UAAA;QAAA;QACA;UACAkB;QACA;MACA;IACA;IACAlE;MACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;IACA;EACA;EACAmE;IACAP;MACA;IACA;IACAC;MACA;IACA;IACAN;MACA;IACA;EACA;EACAa;IACAC;MACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;UACA;YACA;cACAC;YACA;UACA;QACA;MACA;MACA;QACA;UACA;YACA;cACAA;YACA;UACA;QACA;MACA;MACA;MACA;QAAA9E;QAAAC;MAAA;MACA;MACA;MACA;MACA;QACA8E;MACA;QACAA;MACA;MACA;QACA;QACA;UACAC;QACA;UACA;UACA;YACA;YACA;YACA;cACAA;cACAC;cACAA;YACA;YACA;UACA;YACA;YACAH;cACA;gBACAE;gBACAE;cACA;YACA;UACA;QACA;QACAC;MACA;MACA;MACA;MACAL;QACA;UACAM;YAAA9F;YAAAyC;UAAA;UACAsD;QACA;MACA;MACA;MACA;QACAD;UAAA9F;UAAAyC;QAAA;QACA;QACA;UACA;YACA;YACA;cACA;gBACAuD;cACA;YACA;YACAF;UACA;UACA;QACA;UACA;YACAA;cAAA;cAAA;YAAA;UACA;QACA;QACA;MACA;QACA;UACA;UACA;YACA;cACA;cACA;gBACA;kBACAE;gBACA;cACA;cACAF;YACA;YACA;UACA;YACA;cACA;gBACAA;cACA;YACA;UACA;QACA;MACA;MACAD;MACA;MACA;MACA;IACA;IACAI;MACA;QACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACAC;QACAA;QACAA;MACA;QACA;UACAC;UACAA;QACA;UACA;UACA;UACA;QACA;MACA;MACA;MACA;MACA;QACA;QACA;UACAD;UACA;YACA;UACA;QACA;UACAC;UACAA;UACA;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA,mBACAC,sBAEAC,SAEAC,iCACAC;QACA;QACA;UACA;YACA;UACA;QACA;MACA,GACAC;IACA;IACAC;MAAA;MACA;QACA;MACA;MACA;MACA,uBACAC;QACA;QACA;MACA,GACAC;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;UACAC;UACAA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA,mBACAX,sBAEAC,SAEAC,4BACAC;QACA;UACA;UACA;UACA;UACA;UACA;UACA;YACAL;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;UACA;UACA;UACA;YACA;cACAD;cACAA;cACAA;cACAA;cACAA;cACAA;cACA;YACA;cACAC;cACA;YACA;YACA;UACA;YACAA;YACA;YACA;YACA;YACA;cACA;gBACA;gBACAc,MACAV,kBACAW;kBAAAC;kBAAAC;gBAAA,GACAX;kBACA;oBACA;oBACA;oBACAN;oBACAA;oBACA;sBACA;oBACA;sBACAkB;sBACAA;sBACAA;sBACAA;sBACAvC;wBACAqB;wBACAA;wBACA;sBACA;oBACA;kBACA;oBACA;oBACA;kBACA;gBACA;cACA;gBACA;kBACAA;gBACA;gBACAA;gBACA;kBACA;gBACA;kBACArB;oBACAqB;oBACAA;oBACA;kBACA;gBACA;cACA;YACA;UACA;QACA;UACA;UACA;UACA;YACA;UACA;QACA;MACA,GACAM;IACA;IACAa;MACApD;QACA9D;QACAmH;UASArD;YACAsD;YACAD;cACArD;gBACAuD;gBACAC;cACA;YACA;UACA;QAEA;MACA;IACA;IACAC;MAAA;MACA;QACAzD;UACA9D;UACAmH;YACA;cAAAxH;cAAAiF;gBAAA9E;gBAAA0H;cAAA;YAAA;UACA;QACA;MACA;QACA;QACAX,MACAV,uBACAW;UAAAC;UAAAC;QAAA,GACAX;UACA;YACA;YACA;cAAA1G;cAAAiF;gBAAA9E;gBAAA0H;cAAA;YAAA;UACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;QACA;MACA;MACA;MACA1B;MACAA;QACA;UAAApG;UAAAiF;YAAA9E;YAAA4H;YAAA9D;YAAArD;UAAA;QAAA;QACAwF;MACA;MACAA;QACA;UAAApG;UAAAiF;YAAA9E;YAAA6H;YAAA/D;YAAArD;UAAA;QAAA;MACA;MACAwF;QACA;UAAApG;UAAAiF;YAAA9E;YAAA8H;YAAAhE;YAAArD;UAAA;QAAA;MACA;IACA;IACAsH;MACA9B;IACA;IACA+B;MACA;QACA;QACA;UACA1F;QACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IACA2F;MAAA;MACA;MACA;MACA;QACA;QACA;UACAC;YAAAC;YAAAC;UAAA;QACA;QACAnC;UACAoC;UACAH;UACAI;UACAC;YACA;cACA;YACA;cACA;YACA;UACA;QACA;MACA;QACAtC;UACAsC;YACA;cACA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;QACA,mBACArC,sBAEAC,SACAC,4BAKAC;UACAmC;UACA;YACAA;cAAAN;cAAAC;YAAA;UACA;YACAK;cAAAN;cAAAC;YAAA;UACA;UACA;YACA;cACA;YACA;UACA;YACAM;YACAC;YACA;cACA1C;YACA;YACA;cACA;YACA;YACA;cAAApG;cAAAiF;gBAAA9E;gBAAA4I;kBAAAT;kBAAAC;gBAAA;gBAAAM;gBAAAC;gBAAA7E;gBAAArD;cAAA;YAAA;UACA;QACA,GACA8F;MACA;QACA;UACA;YACA;UACA;QACA;UACAkC;UACAA;YAAAN;YAAAC;UAAA;UACAM;UACAC;UACA;YACA1C;UACA;UACA;YACA;UACA;UACA;YAAApG;YAAAiF;cAAA9E;cAAA4I;gBAAAT;gBAAAC;cAAA;cAAAM;cAAAC;cAAA7E;cAAArD;YAAA;UAAA;QACA;MACA;IACA;IACAoI;MACA;MACAC;MACA;QACA7C;MACA;MACA;QAAApG;QAAAiF;UAAA9E;UAAA4I;UAAA9E;UAAArD;QAAA;MAAA;IACA;IACAsI;MACA;MACA;MACA;MACA;MACA;MACAD;MACA;QACA7C;MACA;MACA;QACA;MACA;MACA;QACAA;MACA;MACA;QAAApG;QAAAiF;UAAA9E;UAAA4I;UAAA9E;UAAArD;QAAA;MAAA;IACA;IACAuI;MACA;MACA;QACA/C;MACA;MACA;QAAApG;QAAAiF;UAAA9E;UAAA4I;UAAA9E;UAAArD;QAAA;MAAA;MACA;QACA;MACA;IACA;IAEAwI;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC3rCA;AAAA;AAAA;AAAA;AAAk8B,CAAgB,u4BAAG,EAAC,C;;;;;;;;;;;ACAt9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./qiun-data-charts.vue?vue&type=template&id=fe947b98&scoped=true&filter-modules=eyJyZGNoYXJ0cyI6eyJ0eXBlIjoicmVuZGVyanMiLCJjb250ZW50IjoiIiwic3RhcnQiOjM4ODkwLCJhdHRycyI6eyJtb2R1bGUiOiJyZGNoYXJ0cyIsImxhbmciOiJqcyJ9LCJlbmQiOjU0NjkyfX0%3D&\"\nvar renderjs\nimport script from \"./qiun-data-charts.vue?vue&type=script&lang=js&\"\nexport * from \"./qiun-data-charts.vue?vue&type=script&lang=js&\"\nimport style0 from \"./qiun-data-charts.vue?vue&type=style&index=0&id=fe947b98&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fe947b98\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qiun-data-charts.vue?vue&type=template&id=fe947b98&scoped=true&filter-modules=eyJyZGNoYXJ0cyI6eyJ0eXBlIjoicmVuZGVyanMiLCJjb250ZW50IjoiIiwic3RhcnQiOjM4ODkwLCJhdHRycyI6eyJtb2R1bGUiOiJyZGNoYXJ0cyIsImxhbmciOiJqcyJ9LCJlbmQiOjU0NjkyfX0%3D&\"", "var components\ntry {\n  components = {\n    qiunLoading: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/qiun-data-charts/components/qiun-loading/qiun-loading\" */ \"@/uni_modules/qiun-data-charts/components/qiun-loading/qiun-loading.vue\"\n      )\n    },\n    qiunError: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/qiun-data-charts/components/qiun-error/qiun-error\" */ \"@/uni_modules/qiun-data-charts/components/qiun-error/qiun-error.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qiun-data-charts.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qiun-data-charts.vue?vue&type=script&lang=js&\"", "<!-- \r\n * qiun-data-charts 秋云高性能跨全端图表组件\r\n * Copyright (c) 2021 QIUN® 秋云 https://www.ucharts.cn All rights reserved.\r\n * Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )\r\n * 复制使用请保留本段注释，感谢支持开源！\r\n * 为方便更多开发者使用，如有更好的建议请提交码云 Pull Requests ！\r\n *\r\n * uCharts®官方网站\r\n * https://www.uCharts.cn\r\n * \r\n * 开源地址:\r\n * https://gitee.com/uCharts/uCharts\r\n * \r\n * uni-app插件市场地址：\r\n * http://ext.dcloud.net.cn/plugin?id=271\r\n * \r\n -->\r\n<template>\r\n  <view class=\"chartsview\" :id=\"'ChartBoxId'+cid\">\r\n    <view v-if=\"mixinDatacomLoading\">\r\n      <!-- 自定义加载状态，请改这里 -->\r\n      <qiun-loading :loadingType=\"loadingType\" />\r\n    </view>\r\n    <view v-if=\"mixinDatacomErrorMessage && errorShow\" @tap=\"reloading\">\r\n      <!-- 自定义错误提示，请改这里 -->\r\n      <qiun-error :errorMessage=\"errorMessage\" />\r\n    </view>\r\n    <!-- APP和H5采用renderjs渲染图表 -->\r\n    <!-- #ifdef APP-VUE || H5 -->\r\n    <block v-if=\"echarts\">\r\n      <view\r\n        :style=\"{ background: background }\"\r\n        style=\"width: 100%;height: 100%;\"\r\n        :data-directory=\"directory\"\r\n        :id=\"'EC'+cid\" \r\n        :prop=\"echartsOpts\" \r\n        :change:prop=\"rdcharts.ecinit\" \r\n        :resize=\"echartsResize\"\r\n        :change:resize=\"rdcharts.ecresize\"\r\n        v-show=\"showchart\"\r\n      />\r\n    </block>\r\n    <block v-else>\r\n      <view\r\n        v-on:tap=\"rdcharts.tap\"\r\n        v-on:mousemove=\"rdcharts.mouseMove\"\r\n        v-on:mousedown=\"rdcharts.mouseDown\"\r\n        v-on:mouseup=\"rdcharts.mouseUp\"\r\n        v-on:touchstart=\"rdcharts.touchStart\"\r\n        v-on:touchmove=\"rdcharts.touchMove\"\r\n        v-on:touchend=\"rdcharts.touchEnd\"\r\n        :id=\"'UC'+cid\"\r\n        :prop=\"uchartsOpts\"\r\n        :change:prop=\"rdcharts.ucinit\"\r\n      >\r\n        <canvas\r\n          :id=\"cid\"\r\n          :canvasId=\"cid\"\r\n          :style=\"{ width: cWidth + 'px', height: cHeight + 'px', background: background }\"\r\n          :disable-scroll=\"disableScroll\"\r\n          @error=\"_error\"\r\n          v-show=\"showchart\"\r\n        />\r\n      </view>\r\n    </block>\r\n    <!-- #endif -->\r\n    <!-- 支付宝小程序 -->\r\n    <!-- #ifdef MP-ALIPAY -->\r\n    <block v-if=\"ontouch\">\r\n      <canvas\r\n        :id=\"cid\"\r\n        :canvasId=\"cid\"\r\n        :width=\"cWidth * pixel\"\r\n        :height=\"cHeight * pixel\"\r\n        :style=\"{ width: cWidth + 'px', height: cHeight + 'px', background: background }\"\r\n        :disable-scroll=\"disScroll\"\r\n        @tap=\"_tap\"\r\n        @touchstart=\"_touchStart\"\r\n        @touchmove=\"_touchMove\"\r\n        @touchend=\"_touchEnd\"\r\n        @error=\"_error\"\r\n        v-show=\"showchart\"\r\n      />\r\n    </block>\r\n    <block v-if=\"!ontouch\">\r\n      <canvas\r\n        :id=\"cid\"\r\n        :canvasId=\"cid\"\r\n        :width=\"cWidth * pixel\"\r\n        :height=\"cHeight * pixel\"\r\n        :style=\"{ width: cWidth + 'px', height: cHeight + 'px', background: background }\"\r\n        :disable-scroll=\"disScroll\"\r\n        @tap=\"_tap\"\r\n        @error=\"_error\"\r\n        v-show=\"showchart\"\r\n      />\r\n    </block>\r\n    <!-- #endif -->\r\n    <!-- 其他小程序通过vue渲染图表 -->\r\n    <!-- #ifdef MP-WEIXIN || MP-BAIDU || MP-QQ || MP-TOUTIAO || MP-KUAISHOU || MP-LARK || MP-JD || MP-360 -->\r\n    <block v-if=\"type2d\">\r\n      <view v-if=\"ontouch\" @tap=\"_tap\">\r\n        <canvas\r\n          :id=\"cid\"\r\n          :canvasId=\"cid\"\r\n          :style=\"{ width: cWidth + 'px', height: cHeight + 'px', background: background }\"\r\n          type=\"2d\"\r\n          :disable-scroll=\"disScroll\"\r\n          @touchstart=\"_touchStart\"\r\n          @touchmove=\"_touchMove\"\r\n          @touchend=\"_touchEnd\"\r\n          @error=\"_error\"\r\n          v-show=\"showchart\"\r\n        />\r\n      </view>\r\n      <view v-if=\"!ontouch\" @tap=\"_tap\">\r\n        <canvas\r\n          :id=\"cid\"\r\n          :canvasId=\"cid\"\r\n          :style=\"{ width: cWidth + 'px', height: cHeight + 'px', background: background }\"\r\n          type=\"2d\"\r\n          :disable-scroll=\"disScroll\"\r\n          @error=\"_error\"\r\n          v-show=\"showchart\"\r\n        />\r\n      </view>\r\n    </block>\r\n    <block v-if=\"!type2d\">\r\n      <view v-if=\"ontouch\" @tap=\"_tap\">\r\n        <canvas\r\n          :id=\"cid\"\r\n          :canvasId=\"cid\"\r\n          :style=\"{ width: cWidth + 'px', height: cHeight + 'px', background: background }\"\r\n          @touchstart=\"_touchStart\"\r\n          @touchmove=\"_touchMove\"\r\n          @touchend=\"_touchEnd\"\r\n          :disable-scroll=\"disScroll\"\r\n          @error=\"_error\"\r\n          v-if=\"showchart\"\r\n        />\r\n      </view>\r\n      <view v-if=\"!ontouch\" >\r\n        <canvas\r\n          :id=\"cid\"\r\n          :canvasId=\"cid\"\r\n          :style=\"{ width: cWidth + 'px', height: cHeight + 'px', background: background }\"\r\n          :disable-scroll=\"disScroll\"\r\n          @tap=\"_tap\"\r\n          @error=\"_error\"\r\n          v-if=\"showchart\"\r\n        />\r\n      </view>\r\n    </block>\r\n    <!-- #endif -->\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport uCharts from '../../js_sdk/u-charts/u-charts.js';\r\nimport cfu from '../../js_sdk/u-charts/config-ucharts.js';\r\n// #ifdef APP-VUE || H5\r\nimport cfe from '../../js_sdk/u-charts/config-echarts.js';\r\n// #endif\r\n\r\nfunction deepCloneAssign(origin = {}, ...args) {\r\n  for (let i in args) {\r\n    for (let key in args[i]) {\r\n      if (args[i].hasOwnProperty(key)) {\r\n        origin[key] = args[i][key] && typeof args[i][key] === 'object' ? deepCloneAssign(Array.isArray(args[i][key]) ? [] : {}, origin[key], args[i][key]) : args[i][key];\r\n      }\r\n    }\r\n  }\r\n  return origin;\r\n}\r\n\r\nfunction formatterAssign(args,formatter) {\r\n  for (let key in args) {\r\n    if(args.hasOwnProperty(key) && args[key] !== null && typeof args[key] === 'object'){\r\n      formatterAssign(args[key],formatter)\r\n    }else if(key === 'format' && typeof args[key] === 'string'){\r\n      args['formatter'] = formatter[args[key]] ? formatter[args[key]] : undefined;\r\n    }\r\n  }\r\n  return args;\r\n}\r\n\r\n// 时间转换函数，为了匹配uniClinetDB读取出的时间与categories不同\r\nfunction getFormatDate(date) {\r\n\tvar seperator = \"-\";\r\n\tvar year = date.getFullYear();\r\n\tvar month = date.getMonth() + 1;\r\n\tvar strDate = date.getDate();\r\n\tif (month >= 1 && month <= 9) {\r\n\t\t\tmonth = \"0\" + month;\r\n\t}\r\n\tif (strDate >= 0 && strDate <= 9) {\r\n\t\t\tstrDate = \"0\" + strDate;\r\n\t}\r\n\tvar currentdate = year + seperator + month + seperator + strDate;\r\n\treturn currentdate;\r\n}\r\n\r\nvar lastMoveTime = null;\r\n/**\r\n * 防抖\r\n *\r\n * @param { Function } fn 要执行的方法\r\n * @param { Number } wait  防抖多少毫秒\r\n *\r\n * 在 vue 中使用（注意：不能使用箭头函数，否则this指向不对，并且不能再次封装如：\r\n * move(){  // 错误调用方式\r\n *   debounce(function () {\r\n *   console.log(this.title);\r\n * }, 1000)}）;\r\n * 应该直接使用：// 正确调用方式\r\n * move: debounce(function () {\r\n *   console.log(this.title);\r\n * }, 1000)\r\n */\r\nfunction debounce(fn, wait) {\r\n  let timer = false;\r\n  return function() {\r\n    clearTimeout(timer);\r\n    timer && clearTimeout(timer);\r\n    timer = setTimeout(() => {\r\n      timer = false;\r\n      fn.apply(this, arguments); // 把参数传进去\r\n    }, wait);\r\n  };\r\n}\r\n\r\nexport default {\r\n  name: 'qiun-data-charts',\r\n  mixins: [uniCloud.mixinDatacom],\r\n  props: {\r\n    type: {\r\n      type: String,\r\n      default: null\r\n    },\r\n    canvasId: {\r\n      type: String,\r\n      default: 'uchartsid'\r\n    },\r\n    canvas2d: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    background: {\r\n      type: String,\r\n      default: 'rgba(0,0,0,0)'\r\n    },\r\n    animation: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chartData: {\r\n      type: Object,\r\n      default() {\r\n        return {\r\n          categories: [],\r\n          series: []\r\n        };\r\n      }\r\n    },\r\n    opts: {\r\n      type: Object,\r\n      default() {\r\n        return {};\r\n      }\r\n    },\r\n    eopts: {\r\n      type: Object,\r\n      default() {\r\n        return {};\r\n      }\r\n    },\r\n    loadingType: {\r\n      type: Number,\r\n      default: 2\r\n    },\r\n    errorShow: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    errorReload: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    errorMessage: {\r\n      type: String,\r\n      default: null\r\n    },\r\n    inScrollView: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    reshow: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    reload: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    disableScroll: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    optsWatch: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    onzoom: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    ontap: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    ontouch: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    onmouse: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    onmovetip: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    echartsH5: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    echartsApp: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    tooltipShow: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    tooltipFormat: {\r\n      type: String,\r\n      default: undefined\r\n    },\r\n    tooltipCustom: {\r\n      type: Object,\r\n      default: undefined\r\n    },\r\n    startDate: {\r\n      type: String,\r\n      default: undefined\r\n    },\r\n    endDate: {\r\n      type: String,\r\n      default: undefined\r\n    },\r\n    textEnum: {\r\n      type: Array,\r\n      default () {\r\n        return []\r\n      }\r\n    },\r\n    groupEnum: {\r\n      type: Array,\r\n      default () {\r\n        return []\r\n      }\r\n    },\r\n    pageScrollTop: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    directory: {\r\n      type: String,\r\n      default: '/'\r\n    },\r\n    tapLegend: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    menus: {\r\n      type: Array,\r\n      default () {\r\n        return []\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      cid: 'uchartsid',\r\n      inWx: false,\r\n      inAli: false,\r\n      inTt: false,\r\n      inBd: false,\r\n      inH5: false,\r\n      inApp: false,\r\n      inWin: false,\r\n      type2d: true,\r\n      disScroll: false,\r\n      openmouse: false,\r\n      pixel: 1,\r\n      cWidth: 375,\r\n      cHeight: 250,\r\n      showchart: false,\r\n      echarts: false,\r\n      echartsResize:{\r\n        state:false\r\n      },\r\n      uchartsOpts: {},\r\n      echartsOpts: {},\r\n      drawData:{},\r\n      lastDrawTime:null,\r\n    };\r\n  },\r\n  created(){\r\n    this.cid = this.canvasId\r\n    if (this.canvasId == 'uchartsid' || this.canvasId == '') {\r\n      let t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'\r\n      let len = t.length\r\n      let id = ''\r\n      for (let i = 0; i < 32; i++) {\r\n        id += t.charAt(Math.floor(Math.random() * len))\r\n      }\r\n      this.cid = id\r\n    }\r\n    const systemInfo = uni.getSystemInfoSync()\r\n    if(systemInfo.platform === 'windows' || systemInfo.platform === 'mac'){\r\n      this.inWin = true;\r\n    }\r\n    // #ifdef MP-WEIXIN\r\n    this.inWx = true;\r\n    if (this.canvas2d === false || systemInfo.platform === 'windows' || systemInfo.platform === 'mac') {\r\n      this.type2d = false;\r\n    }else{\r\n      this.type2d = true;\r\n      this.pixel = systemInfo.pixelRatio;\r\n    }\r\n    // #endif\r\n    //非微信小程序端强制关闭canvas2d模式\r\n    // #ifndef MP-WEIXIN\r\n    this.type2d = false;\r\n    // #endif\r\n    // #ifdef  MP-TOUTIAO || MP-LARK || MP-ALIPAY\r\n    this.type2d = this.canvas2d;\r\n    // #endif\r\n    // #ifdef MP-ALIPAY\r\n    this.inAli = true;\r\n    this.pixel = systemInfo.pixelRatio;\r\n    // #endif\r\n    // #ifdef MP-BAIDU\r\n    this.inBd = true;\r\n    // #endif\r\n    // #ifdef MP-TOUTIAO\r\n    this.inTt = true;\r\n    // #endif\r\n    this.disScroll = this.disableScroll;\r\n  },\r\n  mounted() {\r\n    // #ifdef APP-VUE\r\n    this.inApp = true;\r\n    if (this.echartsApp === true) {\r\n      this.echarts = true;\r\n      this.openmouse = false;\r\n    }\r\n    // #endif\r\n    // #ifdef APP-NVUE\r\n    this.inApp = true;\r\n    this.mixinDatacomLoading = false\r\n    this.mixinDatacomErrorMessage = \"暂不支持NVUE\"\r\n    // #endif\r\n    // #ifdef H5\r\n    this.inH5 = true;\r\n    if(this.inWin === true){\r\n      this.openmouse = this.onmouse;\r\n    }\r\n    if (this.echartsH5 === true) {\r\n      this.echarts = true;\r\n    }\r\n    // #endif\r\n    this.$nextTick(()=>{\r\n      this.beforeInit();\r\n    })\r\n    // #ifndef MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || APP-VUE\r\n    const time = this.inH5 ? 500 : 200;\r\n    const _this = this;\r\n    uni.onWindowResize(\r\n      debounce(function(res) {\r\n        if (_this.mixinDatacomLoading == true) {\r\n          return;\r\n        }\r\n        let errmsg = _this.mixinDatacomErrorMessage;\r\n        if (errmsg !== null && errmsg !== 'null' && errmsg !== '') {\r\n          return;\r\n        }\r\n        if (_this.echarts) {\r\n          _this.echartsResize.state = !_this.echartsResize.state;\r\n        } else {\r\n          _this.resizeHandler();\r\n        }\r\n      }, time)\r\n    );\r\n    // #endif\r\n  },\r\n  destroyed(){\r\n    if(this.echarts === true){\r\n      delete cfe.option[this.cid]\r\n      delete cfe.instance[this.cid]\r\n    }else{\r\n      delete cfu.option[this.cid]\r\n      delete cfu.instance[this.cid]\r\n    }\r\n    // #ifndef MP-ALIPAY || MP-BAIDU || MP-TOUTIAO\r\n    uni.offWindowResize(()=>{})\r\n    // #endif\r\n  },\r\n  watch: {\r\n    chartDataProps: {\r\n      handler(val, oldval) {\r\n        if (typeof val === 'object') {\r\n          if (JSON.stringify(val) !== JSON.stringify(oldval)) {\r\n            this._clearChart();\r\n            if (val.series && val.series.length > 0) {\r\n              this.beforeInit();\r\n            }else{\r\n              this.mixinDatacomLoading = true;\r\n              this.showchart = false;\r\n              this.mixinDatacomErrorMessage = null;\r\n            }\r\n          }\r\n        } else {\r\n          this.mixinDatacomLoading = false;\r\n          this._clearChart();\r\n          this.showchart = false;\r\n          this.mixinDatacomErrorMessage = '参数错误：chartData数据类型错误';\r\n        }\r\n      },\r\n      immediate: false,\r\n      deep: true\r\n    },\r\n    localdata:{\r\n      handler(val, oldval) {\r\n        if (JSON.stringify(val) !== JSON.stringify(oldval)) {\r\n          if (val.length > 0) {\r\n            this.beforeInit();\r\n          }else{\r\n            this.mixinDatacomLoading = true;\r\n            this._clearChart();\r\n            this.showchart = false;\r\n            this.mixinDatacomErrorMessage = null;\r\n          }\r\n        }\r\n      },\r\n      immediate: false,\r\n      deep: true\r\n    },\r\n    optsProps: {\r\n      handler(val, oldval) {\r\n        if (typeof val === 'object') {\r\n          if (JSON.stringify(val) !== JSON.stringify(oldval) && this.echarts === false && this.optsWatch == true) {\r\n            this.checkData(this.drawData);\r\n          }\r\n        } else {\r\n          this.mixinDatacomLoading = false;\r\n          this._clearChart();\r\n          this.showchart = false;\r\n          this.mixinDatacomErrorMessage = '参数错误：opts数据类型错误';\r\n        }\r\n      },\r\n      immediate: false,\r\n      deep: true\r\n    },\r\n    eoptsProps: {\r\n      handler(val, oldval) {\r\n        if (typeof val === 'object') {\r\n          if (JSON.stringify(val) !== JSON.stringify(oldval) && this.echarts === true) {\r\n            this.checkData(this.drawData);\r\n          }\r\n        } else {\r\n          this.mixinDatacomLoading = false;\r\n          this.showchart = false;\r\n          this.mixinDatacomErrorMessage = '参数错误：eopts数据类型错误';\r\n        }\r\n      },\r\n      immediate: false,\r\n      deep: true\r\n    },\r\n    reshow(val, oldval) {\r\n      if (val === true && this.mixinDatacomLoading === false) {\r\n        setTimeout(() => {\r\n          this.mixinDatacomErrorMessage = null;\r\n          this.echartsResize.state = !this.echartsResize.state;\r\n          this.checkData(this.drawData);\r\n        }, 200);\r\n      }\r\n    },\r\n    reload(val, oldval) {\r\n      if (val === true) {\r\n        this.showchart = false;\r\n        this.mixinDatacomErrorMessage = null;\r\n        this.reloading();\r\n      }\r\n    },\r\n    mixinDatacomErrorMessage(val, oldval) {\r\n      if (val) {\r\n        this.emitMsg({name: 'error', params: {type:\"error\", errorShow: this.errorShow, msg: val, id: this.cid}});\r\n        if(this.errorShow){\r\n          console.log('[秋云图表组件]' + val);\r\n        }\r\n      }\r\n    },\r\n    errorMessage(val, oldval) {\r\n      if (val && this.errorShow && val !== null && val !== 'null' && val !== '') {\r\n        this.showchart = false;\r\n        this.mixinDatacomLoading = false;\r\n        this.mixinDatacomErrorMessage = val;\r\n      } else {\r\n        this.showchart = false;\r\n        this.mixinDatacomErrorMessage = null;\r\n        this.reloading();\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    optsProps() {\r\n      return JSON.parse(JSON.stringify(this.opts));\r\n    },\r\n    eoptsProps() {\r\n      return JSON.parse(JSON.stringify(this.eopts));\r\n    },\r\n    chartDataProps() {\r\n      return JSON.parse(JSON.stringify(this.chartData));\r\n    },\r\n  },\r\n  methods: {\r\n    beforeInit(){\r\n      this.mixinDatacomErrorMessage = null;\r\n      if (typeof this.chartData === 'object' && this.chartData != null && this.chartData.series !== undefined && this.chartData.series.length > 0) {\r\n        //拷贝一下chartData，为了opts变更后统一数据来源\r\n        this.drawData = deepCloneAssign({}, this.chartData);\r\n        this.mixinDatacomLoading = false;\r\n        this.showchart = true;\r\n        this.checkData(this.chartData);\r\n      }else if(this.localdata.length>0){\r\n        this.mixinDatacomLoading = false;\r\n        this.showchart = true;\r\n        this.localdataInit(this.localdata);\r\n      }else if(this.collection !== ''){\r\n        this.mixinDatacomLoading = false;\r\n        this.getCloudData();\r\n      }else{\r\n        this.mixinDatacomLoading = true;\r\n      }\r\n    },\r\n    localdataInit(resdata){\r\n      //替换enum类型为正确的描述\r\n      if(this.groupEnum.length>0){\r\n        for (let i = 0; i < resdata.length; i++) {\r\n          for (let j = 0; j < this.groupEnum.length; j++) {\r\n            if(resdata[i].group === this.groupEnum[j].value){\r\n              resdata[i].group = this.groupEnum[j].text\r\n            }\r\n          }\r\n        }\r\n      }\r\n      if(this.textEnum.length>0){\r\n        for (let i = 0; i < resdata.length; i++) {\r\n          for (let j = 0; j < this.textEnum.length; j++) {\r\n            if(resdata[i].text === this.textEnum[j].value){\r\n              resdata[i].text = this.textEnum[j].text\r\n            }\r\n          }\r\n        }\r\n      }\r\n      let needCategories = false;\r\n      let tmpData = {categories:[], series:[]}\r\n      let tmpcategories = []\r\n      let tmpseries = [];\r\n      //拼接categories\r\n      if(this.echarts === true){\r\n        needCategories = cfe.categories.includes(this.type)\r\n      }else{\r\n        needCategories = cfu.categories.includes(this.type)\r\n      }\r\n      if(needCategories === true){\r\n        //如果props中的chartData带有categories，则优先使用chartData的categories\r\n        if(this.chartData && this.chartData.categories && this.chartData.categories.length>0){\r\n          tmpcategories = this.chartData.categories\r\n        }else{\r\n          //如果是日期类型的数据，不管是本地数据还是云数据，都按起止日期自动拼接categories\r\n          if(this.startDate && this.endDate){\r\n            let idate = new Date(this.startDate)\r\n            let edate = new Date(this.endDate)\r\n            while (idate <= edate) {\r\n            \ttmpcategories.push(getFormatDate(idate))\r\n            \tidate = idate.setDate(idate.getDate() + 1)\r\n            \tidate = new Date(idate)\r\n            }\r\n          //否则从结果中去重并拼接categories\r\n          }else{\r\n            let tempckey = {};\r\n            resdata.map(function(item, index) {\r\n              if (item.text != undefined && !tempckey[item.text]) {\r\n                tmpcategories.push(item.text)\r\n                tempckey[item.text] = true\r\n              }\r\n            });\r\n          }\r\n        }\r\n        tmpData.categories = tmpcategories\r\n      }\r\n      //拼接series\r\n      let tempskey = {};\r\n      resdata.map(function(item, index) {\r\n        if (item.group != undefined && !tempskey[item.group]) {\r\n          tmpseries.push({ name: item.group, data: [] });\r\n          tempskey[item.group] = true;\r\n        }\r\n      });\r\n      //如果没有获取到分组名称(可能是带categories的数据，也可能是不带的饼图类)\r\n      if (tmpseries.length == 0) {\r\n        tmpseries = [{ name: '默认分组', data: [] }];\r\n        //如果是需要categories的图表类型\r\n        if(needCategories === true){\r\n          for (let j = 0; j < tmpcategories.length; j++) {\r\n            let seriesdata = 0;\r\n            for (let i = 0; i < resdata.length; i++) {\r\n              if (resdata[i].text == tmpcategories[j]) {\r\n                seriesdata = resdata[i].value;\r\n              }\r\n            }\r\n            tmpseries[0].data.push(seriesdata);\r\n          }\r\n        //如果是饼图类的图表类型\r\n        }else{\r\n          for (let i = 0; i < resdata.length; i++) {\r\n            tmpseries[0].data.push({\"name\": resdata[i].text,\"value\": resdata[i].value});\r\n          }\r\n        }\r\n      //如果有分组名\r\n      } else {\r\n        for (let k = 0; k < tmpseries.length; k++) {\r\n          //如果有categories\r\n          if (tmpcategories.length > 0) {\r\n            for (let j = 0; j < tmpcategories.length; j++) {\r\n              let seriesdata = 0;\r\n              for (let i = 0; i < resdata.length; i++) {\r\n                if (tmpseries[k].name == resdata[i].group && resdata[i].text == tmpcategories[j]) {\r\n                  seriesdata = resdata[i].value;\r\n                }\r\n              }\r\n              tmpseries[k].data.push(seriesdata);\r\n            }\r\n          //如果传了group而没有传text，即没有categories（正常情况下这种数据是不符合数据要求规范的）\r\n          } else {\r\n            for (let i = 0; i < resdata.length; i++) {\r\n              if (tmpseries[k].name == resdata[i].group) {\r\n                tmpseries[k].data.push(resdata[i].value);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      tmpData.series = tmpseries\r\n      //拷贝一下chartData，为了opts变更后统一数据来源\r\n      this.drawData = deepCloneAssign({}, tmpData);\r\n      this.checkData(tmpData)\r\n    },\r\n    reloading() {\r\n      if(this.errorReload === false){\r\n        return;\r\n      }\r\n      this.showchart = false;\r\n      this.mixinDatacomErrorMessage = null;\r\n      if (this.collection !== '') {\r\n        this.mixinDatacomLoading = false;\r\n        this.onMixinDatacomPropsChange(true);\r\n      } else {\r\n        this.beforeInit();\r\n      }\r\n    },\r\n    checkData(anyData) {\r\n      let cid = this.cid\r\n      //复位opts或eopts\r\n      if(this.echarts === true){\r\n        cfe.option[cid] = deepCloneAssign({}, this.eopts);\r\n        cfe.option[cid].id = cid;\r\n        cfe.option[cid].type = this.type;\r\n      }else{\r\n        if (this.type && cfu.type.includes(this.type)) {\r\n          cfu.option[cid] = deepCloneAssign({}, cfu[this.type], this.opts);\r\n          cfu.option[cid].canvasId = cid;\r\n        } else {\r\n          this.mixinDatacomLoading = false;\r\n          this.showchart = false;\r\n          this.mixinDatacomErrorMessage = '参数错误：props参数中type类型不正确';\r\n        }\r\n      }\r\n      //挂载categories和series\r\n      let newData = deepCloneAssign({}, anyData);\r\n      if (newData.series !== undefined && newData.series.length > 0) {\r\n        this.mixinDatacomErrorMessage = null;\r\n        if (this.echarts === true) {\r\n          cfe.option[cid].chartData = newData;\r\n          this.$nextTick(()=>{\r\n            this.init()\r\n          })\r\n        }else{\r\n          cfu.option[cid].categories = newData.categories;\r\n          cfu.option[cid].series = newData.series;\r\n          this.$nextTick(()=>{\r\n            this.init()\r\n          })\r\n        }\r\n      }\r\n    },\r\n    resizeHandler() {\r\n      //渲染防抖\r\n      let currTime = Date.now();\r\n      let lastDrawTime = this.lastDrawTime?this.lastDrawTime:currTime-3000;\r\n      let duration = currTime - lastDrawTime;\r\n      if (duration < 1000) return;\r\n      let chartdom = uni\r\n        .createSelectorQuery()\r\n        // #ifndef MP-ALIPAY\r\n        .in(this)\r\n        // #endif\r\n        .select('#ChartBoxId'+this.cid)\r\n        .boundingClientRect(data => {\r\n          this.showchart = true;\r\n          if (data.width > 0 && data.height > 0) {\r\n            if (data.width !== this.cWidth || data.height !== this.cHeight) {\r\n              this.checkData(this.drawData)\r\n            }\r\n          }\r\n        })\r\n        .exec();\r\n    },\r\n    getCloudData() {\r\n      if (this.mixinDatacomLoading == true) {\r\n        return;\r\n      }\r\n      this.mixinDatacomLoading = true;\r\n      this.mixinDatacomGet()\r\n        .then(res => {\r\n          this.mixinDatacomResData = res.result.data;\r\n          this.localdataInit(this.mixinDatacomResData);\r\n        })\r\n        .catch(err => {\r\n          this.mixinDatacomLoading = false;\r\n          this.showchart = false;\r\n          this.mixinDatacomErrorMessage = '请求错误：' + err;\r\n        });\r\n    },\r\n    onMixinDatacomPropsChange(needReset, changed) {\r\n      if (needReset == true && this.collection !== '') {\r\n        this.showchart = false;\r\n        this.mixinDatacomErrorMessage = null;\r\n        this._clearChart();\r\n        this.getCloudData();\r\n      }\r\n    },\r\n    _clearChart() {\r\n      let cid = this.cid\r\n      if (this.echarts !== true && cfu.option[cid] && cfu.option[cid].context) {\r\n        const ctx = cfu.option[cid].context;\r\n        if(typeof ctx === \"object\" && !!!cfu.option[cid].update){\r\n          ctx.clearRect(0, 0, this.cWidth*this.pixel, this.cHeight*this.pixel);\r\n          ctx.draw();\r\n        }\r\n      }\r\n    },\r\n    init() {\r\n      let cid = this.cid\r\n      let chartdom = uni\r\n        .createSelectorQuery()\r\n        // #ifndef MP-ALIPAY\r\n        .in(this)\r\n        // #endif\r\n        .select('#ChartBoxId'+cid)\r\n        .boundingClientRect(data => {\r\n          if (data.width > 0 && data.height > 0) {\r\n            this.mixinDatacomLoading = false;\r\n            this.showchart = true;\r\n            this.lastDrawTime = Date.now();\r\n            this.cWidth = data.width;\r\n            this.cHeight = data.height;\r\n            if(this.echarts !== true){\r\n              cfu.option[cid].background = this.background == 'rgba(0,0,0,0)' ? '#FFFFFF' : this.background;\r\n              cfu.option[cid].canvas2d = this.type2d;\r\n              cfu.option[cid].pixelRatio = this.pixel;\r\n              cfu.option[cid].animation = this.animation;\r\n              cfu.option[cid].width = data.width * this.pixel;\r\n              cfu.option[cid].height = data.height * this.pixel;\r\n              cfu.option[cid].onzoom = this.onzoom;\r\n              cfu.option[cid].ontap = this.ontap;\r\n              cfu.option[cid].ontouch = this.ontouch;\r\n              cfu.option[cid].onmouse = this.openmouse;\r\n              cfu.option[cid].onmovetip = this.onmovetip;\r\n              cfu.option[cid].tooltipShow = this.tooltipShow;\r\n              cfu.option[cid].tooltipFormat = this.tooltipFormat;\r\n              cfu.option[cid].tooltipCustom = this.tooltipCustom;\r\n              cfu.option[cid].inScrollView = this.inScrollView;\r\n              cfu.option[cid].lastDrawTime = this.lastDrawTime;\r\n              cfu.option[cid].tapLegend = this.tapLegend;\r\n            }\r\n            //如果是H5或者App端，采用renderjs渲染图表\r\n            if (this.inH5 || this.inApp) {\r\n              if (this.echarts == true) {\r\n                cfe.option[cid].ontap = this.ontap;\r\n                cfe.option[cid].onmouse = this.openmouse;\r\n                cfe.option[cid].tooltipShow = this.tooltipShow;\r\n                cfe.option[cid].tooltipFormat = this.tooltipFormat;\r\n                cfe.option[cid].tooltipCustom = this.tooltipCustom;\r\n                cfe.option[cid].lastDrawTime = this.lastDrawTime;\r\n                this.echartsOpts = deepCloneAssign({}, cfe.option[cid]);\r\n              } else {\r\n                cfu.option[cid].rotateLock = cfu.option[cid].rotate;\r\n                this.uchartsOpts = deepCloneAssign({}, cfu.option[cid]);\r\n              }\r\n            //如果是小程序端，采用uCharts渲染\r\n            } else {\r\n              cfu.option[cid] = formatterAssign(cfu.option[cid],cfu.formatter)\r\n              this.mixinDatacomErrorMessage = null;\r\n              this.mixinDatacomLoading = false;\r\n              this.showchart = true;\r\n              this.$nextTick(()=>{\r\n                if (this.type2d === true) {\r\n                  const query = uni.createSelectorQuery().in(this)\r\n                  query\r\n                    .select('#' + cid)\r\n                    .fields({ node: true, size: true })\r\n                    .exec(res => {\r\n                      if (res[0]) {\r\n                        const canvas = res[0].node;\r\n                        const ctx = canvas.getContext('2d');\r\n                        cfu.option[cid].context = ctx;\r\n                        cfu.option[cid].rotateLock = cfu.option[cid].rotate;\r\n                        if(cfu.instance[cid] && cfu.option[cid] && cfu.option[cid].update === true){\r\n                          this._updataUChart(cid)\r\n                        }else{\r\n                          canvas.width = data.width * this.pixel;\r\n                          canvas.height = data.height * this.pixel;\r\n                          canvas._width = data.width * this.pixel;\r\n                          canvas._height = data.height * this.pixel;\r\n                          setTimeout(()=>{\r\n                            cfu.option[cid].context.restore();\r\n                            cfu.option[cid].context.save();\r\n                            this._newChart(cid)\r\n                          },100)\r\n                        }\r\n                      } else {\r\n                        this.showchart = false;\r\n                        this.mixinDatacomErrorMessage = '参数错误：开启2d模式后，未获取到dom节点，canvas-id:' + cid;\r\n                      }\r\n                    });\r\n                } else {\r\n                  if(this.inAli){\r\n                    cfu.option[cid].rotateLock = cfu.option[cid].rotate;\r\n                  }\r\n                  cfu.option[cid].context = uni.createCanvasContext(cid, this);\r\n                  if(cfu.instance[cid] && cfu.option[cid] && cfu.option[cid].update === true){\r\n                    this._updataUChart(cid)\r\n                  }else{\r\n                    setTimeout(()=>{\r\n                      cfu.option[cid].context.restore();\r\n                      cfu.option[cid].context.save();\r\n                      this._newChart(cid)\r\n                    },100)\r\n                  }\r\n                }\r\n              })\r\n            }\r\n          } else {\r\n            this.mixinDatacomLoading = false;\r\n            this.showchart = false;\r\n            if (this.reshow == true) {\r\n              this.mixinDatacomErrorMessage = '布局错误：未获取到父元素宽高尺寸！canvas-id:' + cid;\r\n            }\r\n          }\r\n        })\r\n        .exec();\r\n    },\r\n    saveImage(){\r\n    \tuni.canvasToTempFilePath({\r\n    \t  canvasId: this.cid,\r\n    \t  success: res=>{\r\n    \t    //#ifdef H5\r\n    \t\t\tvar a = document.createElement(\"a\");\r\n    \t\t\ta.href = res.tempFilePath;\r\n    \t\t\ta.download = this.cid;\r\n    \t\t\ta.target = '_blank'\r\n    \t\t\ta.click();\r\n    \t    //#endif\r\n    \t    //#ifndef H5\r\n    \t      uni.saveImageToPhotosAlbum({\r\n              filePath: res.tempFilePath,\r\n              success: function () {\r\n                uni.showToast({\r\n                  title: '保存成功',\r\n                  duration: 2000\r\n                });\r\n              }\r\n    \t      });\r\n    \t    //#endif\r\n    \t  } \r\n    \t},this);\r\n    },\r\n    getImage(){\r\n      if(this.type2d == false){\r\n        uni.canvasToTempFilePath({\r\n          canvasId: this.cid,\r\n          success: res=>{\r\n            this.emitMsg({name: 'getImage', params: {type:\"getImage\", base64: res.tempFilePath}});\r\n          }\r\n        },this);\r\n      }else{\r\n        const query = uni.createSelectorQuery().in(this)\r\n        query\r\n          .select('#' + this.cid)\r\n          .fields({ node: true, size: true })\r\n          .exec(res => {\r\n            if (res[0]) {\r\n              const canvas = res[0].node;\r\n              this.emitMsg({name: 'getImage', params: {type:\"getImage\", base64: canvas.toDataURL('image/png')}});\r\n            }\r\n          });\r\n      }\r\n    },\r\n    // #ifndef APP-VUE || H5\r\n    _newChart(cid) {\r\n      if (this.mixinDatacomLoading == true) {\r\n        return;\r\n      }\r\n      this.showchart = true;\r\n      cfu.instance[cid] = new uCharts(cfu.option[cid]);\r\n      cfu.instance[cid].addEventListener('renderComplete', () => {\r\n        this.emitMsg({name: 'complete', params: {type:\"complete\", complete: true, id: cid, opts: cfu.instance[cid].opts}});\r\n        cfu.instance[cid].delEventListener('renderComplete')\r\n      });\r\n      cfu.instance[cid].addEventListener('scrollLeft', () => {\r\n        this.emitMsg({name: 'scrollLeft', params: {type:\"scrollLeft\", scrollLeft: true, id: cid, opts: cfu.instance[cid].opts}});\r\n      });\r\n      cfu.instance[cid].addEventListener('scrollRight', () => {\r\n        this.emitMsg({name: 'scrollRight', params: {type:\"scrollRight\", scrollRight: true, id: cid, opts: cfu.instance[cid].opts}});\r\n      });\r\n    },\r\n    _updataUChart(cid) {\r\n      cfu.instance[cid].updateData(cfu.option[cid])\r\n    },\r\n    _tooltipDefault(item, category, index, opts) {\r\n      if (category) {\r\n        let data = item.data\r\n        if(typeof item.data === \"object\"){\r\n          data = item.data.value\r\n        }\r\n        return category + ' ' + item.name + ':' + data;\r\n      } else {\r\n        if (item.properties && item.properties.name) {\r\n          return item.properties.name;\r\n        } else {\r\n          return item.name + ':' + item.data;\r\n        }\r\n      }\r\n    },\r\n    _showTooltip(e) {\r\n      let cid = this.cid\r\n      let tc = cfu.option[cid].tooltipCustom\r\n      if (tc && tc !== undefined && tc !== null) {\r\n        let offset = undefined;\r\n        if (tc.x >= 0 && tc.y >= 0) {\r\n          offset = { x: tc.x, y: tc.y + 10 };\r\n        }\r\n        cfu.instance[cid].showToolTip(e, {\r\n          index: tc.index,\r\n          offset: offset,\r\n          textList: tc.textList,\r\n          formatter: (item, category, index, opts) => {\r\n            if (typeof cfu.option[cid].tooltipFormat === 'string' && cfu.formatter[cfu.option[cid].tooltipFormat]) {\r\n              return cfu.formatter[cfu.option[cid].tooltipFormat](item, category, index, opts);\r\n            } else {\r\n              return this._tooltipDefault(item, category, index, opts);\r\n            }\r\n          }\r\n        });\r\n      } else {\r\n        cfu.instance[cid].showToolTip(e, {\r\n          formatter: (item, category, index, opts) => {\r\n            if (typeof cfu.option[cid].tooltipFormat === 'string' && cfu.formatter[cfu.option[cid].tooltipFormat]) {\r\n              return cfu.formatter[cfu.option[cid].tooltipFormat](item, category, index, opts);\r\n            } else {\r\n              return this._tooltipDefault(item, category, index, opts);\r\n            }\r\n          }\r\n        });\r\n      }\r\n    },\r\n    _tap(e,move) {\r\n      let cid = this.cid\r\n      let currentIndex = null;\r\n      let legendIndex = null;\r\n      if (this.inScrollView === true || this.inAli) {\r\n        let chartdom = uni\r\n          .createSelectorQuery()\r\n          // #ifndef MP-ALIPAY\r\n          .in(this)\r\n          .select('#ChartBoxId'+cid)\r\n          // #endif\r\n          // #ifdef MP-ALIPAY\r\n          .select('#'+this.cid)\r\n          // #endif\r\n          .boundingClientRect(data => {\r\n            e.changedTouches=[];\r\n            if (this.inAli) {\r\n              e.changedTouches.unshift({ x: e.detail.clientX - data.left, y: e.detail.clientY - data.top});\r\n            }else{\r\n              e.changedTouches.unshift({ x: e.detail.x - data.left, y: e.detail.y - data.top - this.pageScrollTop});\r\n            }\r\n            if(move){\r\n              if (this.tooltipShow === true) {\r\n                this._showTooltip(e);\r\n              }\r\n            }else{\r\n              currentIndex = cfu.instance[cid].getCurrentDataIndex(e);\r\n              legendIndex = cfu.instance[cid].getLegendDataIndex(e);\r\n              if(this.tapLegend === true){\r\n                cfu.instance[cid].touchLegend(e);\r\n              }\r\n              if (this.tooltipShow === true) {\r\n                this._showTooltip(e);\r\n              }\r\n              this.emitMsg({name: 'getIndex', params: { type:\"getIndex\", event:{ x: e.detail.x - data.left, y: e.detail.y - data.top }, currentIndex: currentIndex, legendIndex: legendIndex, id: cid, opts: cfu.instance[cid].opts}});\r\n            }\r\n          })\r\n          .exec();\r\n      } else {\r\n        if(move){\r\n          if (this.tooltipShow === true) {\r\n            this._showTooltip(e);\r\n          }\r\n        }else{\r\n          e.changedTouches=[];\r\n          e.changedTouches.unshift({ x: e.detail.x - e.currentTarget.offsetLeft, y: e.detail.y - e.currentTarget.offsetTop });\r\n          currentIndex = cfu.instance[cid].getCurrentDataIndex(e);\r\n          legendIndex = cfu.instance[cid].getLegendDataIndex(e);\r\n          if(this.tapLegend === true){\r\n            cfu.instance[cid].touchLegend(e);\r\n          }\r\n          if (this.tooltipShow === true) {\r\n            this._showTooltip(e);\r\n          }\r\n          this.emitMsg({name: 'getIndex', params: {type:\"getIndex\", event:{ x: e.detail.x, y: e.detail.y - e.currentTarget.offsetTop }, currentIndex: currentIndex, legendIndex: legendIndex, id: cid, opts: cfu.instance[cid].opts}});\r\n        }\r\n      }\r\n    },\r\n    _touchStart(e) {\r\n      let cid = this.cid\r\n      lastMoveTime=Date.now();\r\n      if(cfu.option[cid].enableScroll === true && e.touches.length == 1){\r\n        cfu.instance[cid].scrollStart(e);\r\n      }\r\n      this.emitMsg({name:'getTouchStart', params:{type:\"touchStart\", event:e.changedTouches[0], id:cid, opts: cfu.instance[cid].opts}});\r\n    },\r\n    _touchMove(e) {\r\n      let cid = this.cid\r\n      let currMoveTime = Date.now();\r\n      let duration = currMoveTime - lastMoveTime;\r\n      let touchMoveLimit = cfu.option[cid].touchMoveLimit || 24;\r\n      if (duration < Math.floor(1000 / touchMoveLimit)) return;//每秒60帧\r\n      lastMoveTime = currMoveTime;\r\n      if(cfu.option[cid].enableScroll === true && e.changedTouches.length == 1){\r\n        cfu.instance[cid].scroll(e);\r\n      }\r\n      if(this.ontap === true && cfu.option[cid].enableScroll === false && this.onmovetip === true){\r\n        this._tap(e,true)\r\n      }\r\n      if(this.ontouch === true && cfu.option[cid].enableScroll === true && this.onzoom === true && e.changedTouches.length == 2){\r\n        cfu.instance[cid].dobuleZoom(e);\r\n      }\r\n      this.emitMsg({name: 'getTouchMove', params: {type:\"touchMove\", event:e.changedTouches[0], id: cid, opts: cfu.instance[cid].opts}});\r\n    },\r\n    _touchEnd(e) {\r\n      let cid = this.cid\r\n      if(cfu.option[cid].enableScroll === true && e.touches.length == 0){\r\n        cfu.instance[cid].scrollEnd(e);\r\n      }\r\n      this.emitMsg({name:'getTouchEnd', params:{type:\"touchEnd\", event:e.changedTouches[0], id:cid, opts: cfu.instance[cid].opts}});\r\n      if(this.ontap === true && cfu.option[cid].enableScroll === false && this.onmovetip === true){\r\n        this._tap(e,true)\r\n      }\r\n    },\r\n    // #endif\r\n    _error(e) {\r\n      this.mixinDatacomErrorMessage = e.detail.errMsg;\r\n    },\r\n    emitMsg(msg) {\r\n      this.$emit(msg.name, msg.params);\r\n    },\r\n    getRenderType() {\r\n      //防止如果开启echarts且父元素为v-if的情况renderjs监听不到prop变化的问题\r\n      if(this.echarts===true && this.mixinDatacomLoading===false){\r\n        this.beforeInit()\r\n      }\r\n    },\r\n    toJSON(){\r\n      return this\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<!-- #ifdef APP-VUE || H5 -->\r\n<script module=\"rdcharts\" lang=\"renderjs\">\r\nimport uChartsRD from '../../js_sdk/u-charts/u-charts.js';\r\nimport cfu from '../../js_sdk/u-charts/config-ucharts.js';\r\nimport cfe from '../../js_sdk/u-charts/config-echarts.js';\r\n\r\nvar that = {};\r\nvar rootdom = null;\r\n\r\nfunction rddeepCloneAssign(origin = {}, ...args) {\r\n  for (let i in args) {\r\n    for (let key in args[i]) {\r\n      if (args[i].hasOwnProperty(key)) {\r\n        origin[key] = args[i][key] && typeof args[i][key] === 'object' ? rddeepCloneAssign(Array.isArray(args[i][key]) ? [] : {}, origin[key], args[i][key]) : args[i][key];\r\n      }\r\n    }\r\n  }\r\n  return origin;\r\n}\r\n\r\nfunction rdformatterAssign(args,formatter) {\r\n  for (let key in args) {\r\n    if(args.hasOwnProperty(key) && args[key] !== null && typeof args[key] === 'object'){\r\n      rdformatterAssign(args[key],formatter)\r\n    }else if(key === 'format' && typeof args[key] === 'string'){\r\n      args['formatter'] = formatter[args[key]] ? formatter[args[key]] : undefined;\r\n    }\r\n  }\r\n  return args;\r\n}\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      rid:null\r\n    }\r\n  },\r\n  mounted() {\r\n    rootdom = {top:0,left:0}\r\n    // #ifdef H5\r\n    let dm = document.querySelectorAll('uni-main')[0]\r\n    if(dm === undefined){\r\n      dm = document.querySelectorAll('uni-page-wrapper')[0]\r\n    }\r\n    rootdom = {top:dm.offsetTop,left:dm.offsetLeft}\r\n    // #endif\r\n    setTimeout(()=>{\r\n      if(this.rid === null){\r\n        this.$ownerInstance && this.$ownerInstance.callMethod('getRenderType')\r\n      }\r\n    },200)\r\n  },\r\n  destroyed(){\r\n    delete cfu.option[this.rid]\r\n    delete cfu.instance[this.rid]\r\n    delete cfe.option[this.rid]\r\n    delete cfe.instance[this.rid]\r\n  },\r\n  methods: {\r\n    //==============以下是ECharts的方法====================\r\n    ecinit(newVal, oldVal, owner, instance){\r\n      let cid = JSON.stringify(newVal.id)\r\n      this.rid = cid\r\n      that[cid] = this.$ownerInstance || instance\r\n      let eopts = JSON.parse(JSON.stringify(newVal))\r\n      let type = eopts.type;\r\n      //载入并覆盖默认配置\r\n      if (type && cfe.type.includes(type)) {\r\n        cfe.option[cid] = rddeepCloneAssign({}, cfe[type], eopts);\r\n      }else{\r\n        cfe.option[cid] = rddeepCloneAssign({}, eopts);\r\n      }\r\n      let newData = eopts.chartData;\r\n      if(newData){\r\n        //挂载categories和series\r\n        if(cfe.option[cid].xAxis && cfe.option[cid].xAxis.type && cfe.option[cid].xAxis.type === 'category'){\r\n          cfe.option[cid].xAxis.data = newData.categories\r\n        }\r\n        if(cfe.option[cid].yAxis && cfe.option[cid].yAxis.type && cfe.option[cid].yAxis.type === 'category'){\r\n          cfe.option[cid].yAxis.data = newData.categories\r\n        }\r\n        cfe.option[cid].series = []\r\n        for (var i = 0; i < newData.series.length; i++) {\r\n          cfe.option[cid].seriesTemplate = cfe.option[cid].seriesTemplate ? cfe.option[cid].seriesTemplate : {}\r\n          let Template = rddeepCloneAssign({},cfe.option[cid].seriesTemplate,newData.series[i])\r\n          cfe.option[cid].series.push(Template)\r\n        }\r\n      }\r\n      \r\n      if (typeof window.echarts === 'object') {\r\n          this.newEChart()\r\n      }else{\r\n        const script = document.createElement('script')\r\n        // #ifdef APP-VUE\r\n        script.src = './uni_modules/qiun-data-charts/static/app-plus/echarts.min.js'\r\n        // #endif\r\n        // #ifdef H5\r\n        const rooturl = window.location.origin\r\n        const directory = instance.getDataset().directory\r\n        script.src = rooturl + directory + 'uni_modules/qiun-data-charts/static/h5/echarts.min.js'\r\n        // #endif\r\n        script.onload = this.newEChart\r\n        document.head.appendChild(script)\r\n      }\r\n    },\r\n    ecresize(newVal, oldVal, owner, instance){\r\n      if(cfe.instance[this.rid]){\r\n        cfe.instance[this.rid].resize()\r\n      }\r\n    },\r\n    newEChart(){\r\n      let cid = this.rid\r\n      if(cfe.instance[cid] === undefined){\r\n        cfe.instance[cid] = echarts.init(that[cid].$el.children[0])\r\n        //ontap开启后才触发click事件\r\n        if(cfe.option[cid].ontap === true){\r\n          cfe.instance[cid].on('click', resdata => {\r\n            let event = JSON.parse(JSON.stringify({\r\n              x:resdata.event.offsetX,y:resdata.event.offsetY\r\n            }))\r\n            that[cid].callMethod('emitMsg',{name:\"getIndex\", params:{type:\"getIndex\", event:event, currentIndex:resdata.dataIndex, value:resdata.data, seriesName: resdata.seriesName,id:cid}})\r\n          })\r\n          // 增加ECharts的highlight消息，实现按下移动返回索引功能。add by onefish 创建于 2021-12-11 09:50\r\n          cfe.instance[cid].on('highlight', resdata => {\r\n            that[cid].callMethod('emitMsg',{name:\"getHighlight\", params:{type:\"highlight\", res:resdata, id:cid}})\r\n          })\r\n        }\r\n        this.updataEChart(cid,cfe.option[cid])\r\n      }else{\r\n        this.updataEChart(cid,cfe.option[cid])\r\n      }\r\n    },\r\n    updataEChart(cid,option){\r\n      //替换option内format属性为formatter的预定义方法\r\n      option = rdformatterAssign(option,cfe.formatter)\r\n      if(option.tooltip){\r\n        option.tooltip.show = option.tooltipShow?true:false;\r\n        option.tooltip.position = this.tooltipPosition()\r\n        //tooltipFormat方法，替换组件的tooltipFormat为config-echarts.js内对应的方法\r\n        if (typeof option.tooltipFormat === 'string' && cfe.formatter[option.tooltipFormat]) {\r\n          option.tooltip.formatter = option.tooltip.formatter ? option.tooltip.formatter : cfe.formatter[option.tooltipFormat]\r\n        }\r\n      }\r\n      // 颜色渐变添加的方法\r\n      if (option.series) {\r\n      \tfor (let i in option.series) {\r\n      \t\tlet linearGradient = option.series[i].linearGradient\r\n      \t\tif (linearGradient) {\r\n      \t\t\toption.series[i].color = new echarts.graphic.LinearGradient(linearGradient[0],linearGradient[1],linearGradient[2],linearGradient[3],linearGradient[4])\r\n      \t\t}\r\n      \t}\r\n      }\r\n      cfe.instance[cid].setOption(option, option.notMerge)\r\n      cfe.instance[cid].on('finished', function(){\r\n        that[cid].callMethod('emitMsg',{name:\"complete\",params:{type:\"complete\",complete:true,id:cid}})\r\n        if(cfe.instance[cid]){\r\n          cfe.instance[cid].off('finished')\r\n        }\r\n      });\r\n\r\n      //修复init初始化实例获取宽高不正确问题\r\n      if(\r\n        typeof that[cid].$el.children[0].clientWidth != 'undefined' &&\r\n          (\r\n            Math.abs( that[cid].$el.children[0].clientWidth - cfe.instance[cid].getWidth() )>3 ||\r\n            Math.abs( that[cid].$el.children[0].clientHeight - cfe.instance[cid].getHeight() )>3\r\n          )\r\n      ){this.ecresize();}\r\n    },\r\n    tooltipPosition(){\r\n      return (point, params, dom, rect, size) => {\r\n      \tlet x = point[0]\r\n      \tlet y = point[1]\r\n      \tlet viewWidth = size.viewSize[0]\r\n      \tlet viewHeight = size.viewSize[1]\r\n      \tlet boxWidth = size.contentSize[0]\r\n      \tlet boxHeight = size.contentSize[1]\r\n      \tlet posX = x + 30 \r\n      \tlet posY = y + 30 \r\n      \tif (posX + boxWidth > viewWidth) { \r\n      \t\tposX = x - boxWidth - 30\r\n      \t}\r\n      \tif (posY + boxHeight > viewHeight) {\r\n      \t\tposY = y - boxHeight - 30\r\n      \t}\r\n      \treturn [posX, posY]\r\n      }\r\n    },\r\n    //==============以下是uCharts的方法====================\r\n    ucinit(newVal, oldVal, owner, instance){\r\n      if(JSON.stringify(newVal) == JSON.stringify(oldVal)){\r\n        return;\r\n      }\r\n      if(!newVal.canvasId){\r\n        return;\r\n      }\r\n      let cid = JSON.parse(JSON.stringify(newVal.canvasId))\r\n      this.rid = cid\r\n      that[cid] = this.$ownerInstance || instance\r\n      cfu.option[cid] = JSON.parse(JSON.stringify(newVal))\r\n      cfu.option[cid] = rdformatterAssign(cfu.option[cid],cfu.formatter)\r\n      let canvasdom = document.getElementById(cid)\r\n      if(canvasdom && canvasdom.children[0]){\r\n        cfu.option[cid].context = canvasdom.children[0].getContext(\"2d\")\r\n        if(cfu.instance[cid] && cfu.option[cid] && cfu.option[cid].update === true){\r\n          this.updataUChart()\r\n        }else{\r\n          setTimeout(()=>{\r\n            cfu.option[cid].context.restore();\r\n            cfu.option[cid].context.save();\r\n            this.newUChart()\r\n          },100)\r\n        }\r\n      }\r\n    },\r\n    newUChart() {\r\n      let cid = this.rid\r\n      cfu.instance[cid] = new uChartsRD(cfu.option[cid])\r\n      cfu.instance[cid].addEventListener('renderComplete', () => {\r\n        that[cid].callMethod('emitMsg',{name:\"complete\",params:{type:\"complete\",complete:true,id:cid, opts: cfu.instance[cid].opts}})\r\n        cfu.instance[cid].delEventListener('renderComplete')\r\n      });\r\n      cfu.instance[cid].addEventListener('scrollLeft', () => {\r\n        that[cid].callMethod('emitMsg',{name:\"scrollLeft\",params:{type:\"scrollLeft\",scrollLeft:true,id:cid, opts: cfu.instance[cid].opts}})\r\n      });\r\n      cfu.instance[cid].addEventListener('scrollRight', () => {\r\n        that[cid].callMethod('emitMsg',{name:\"scrollRight\",params:{type:\"scrollRight\",scrollRight:true,id:cid, opts: cfu.instance[cid].opts}})\r\n      });\r\n    },\r\n    updataUChart() {\r\n      let cid = this.rid\r\n      cfu.instance[cid].updateData(cfu.option[cid])\r\n    },\r\n    tooltipDefault(item, category, index, opts) {\r\n      if (category) {\r\n        let data = item.data\r\n        if(typeof item.data === \"object\"){\r\n          data = item.data.value\r\n        }\r\n        return category + ' ' + item.name + ':' + data;\r\n      } else {\r\n        if (item.properties && item.properties.name) {\r\n          return item.properties.name ;\r\n        } else {\r\n          return item.name + ':' + item.data;\r\n        }\r\n      }\r\n    },\r\n    showTooltip(e,cid) {\r\n      let tc = cfu.option[cid].tooltipCustom\r\n      if (tc && tc !== undefined && tc !== null) {\r\n        let offset = undefined;\r\n        if (tc.x >= 0 && tc.y >= 0) {\r\n          offset = { x: tc.x, y: tc.y + 10 };\r\n        }\r\n        cfu.instance[cid].showToolTip(e, {\r\n          index: tc.index,\r\n          offset: offset,\r\n          textList: tc.textList,\r\n          formatter: (item, category, index, opts) => {\r\n            if (typeof cfu.option[cid].tooltipFormat === 'string' && cfu.formatter[cfu.option[cid].tooltipFormat]) {\r\n              return cfu.formatter[cfu.option[cid].tooltipFormat](item, category, index, opts);\r\n            } else {\r\n              return this.tooltipDefault(item, category, index, opts);\r\n            }\r\n          }\r\n        });\r\n      } else {\r\n        cfu.instance[cid].showToolTip(e, {\r\n          formatter: (item, category, index, opts) => {\r\n            if (typeof cfu.option[cid].tooltipFormat === 'string' && cfu.formatter[cfu.option[cid].tooltipFormat]) {\r\n              return cfu.formatter[cfu.option[cid].tooltipFormat](item, category, index, opts);\r\n            } else {\r\n              return this.tooltipDefault(item, category, index, opts);\r\n            }\r\n          }\r\n        });\r\n      }\r\n    },\r\n    tap(e) {\r\n      let cid = this.rid\r\n      let ontap = cfu.option[cid].ontap\r\n      let tooltipShow = cfu.option[cid].tooltipShow\r\n      let tapLegend = cfu.option[cid].tapLegend\r\n      if(ontap == false) return;\r\n      let currentIndex=null\r\n      let legendIndex=null\r\n      let rchartdom = document.getElementById('UC'+cid).getBoundingClientRect()\r\n      let tmpe = {}\r\n      if(e.detail.x){//tap或者click的事件\r\n        tmpe = { x: e.detail.x - rchartdom.left, y:e.detail.y - rchartdom.top + rootdom.top}\r\n      }else{//mouse的事件\r\n        tmpe = { x: e.clientX - rchartdom.left, y:e.clientY - rchartdom.top + rootdom.top}\r\n      }\r\n      e.changedTouches = [];\r\n      e.changedTouches.unshift(tmpe)\r\n      currentIndex=cfu.instance[cid].getCurrentDataIndex(e)\r\n      legendIndex=cfu.instance[cid].getLegendDataIndex(e)\r\n      if(tapLegend === true){\r\n        cfu.instance[cid].touchLegend(e);\r\n      }\r\n      if(tooltipShow==true){\r\n        this.showTooltip(e,cid)\r\n      }\r\n      that[cid].callMethod('emitMsg',{name:\"getIndex\",params:{type:\"getIndex\",event:tmpe,currentIndex:currentIndex,legendIndex:legendIndex,id:cid, opts: cfu.instance[cid].opts}})\r\n    },\r\n    touchStart(e) {\r\n      let cid = this.rid\r\n      let ontouch = cfu.option[cid].ontouch\r\n      if(ontouch == false) return;\r\n      if(cfu.option[cid].enableScroll === true && e.touches.length == 1){\r\n        cfu.instance[cid].scrollStart(e);\r\n      }\r\n      that[cid].callMethod('emitMsg',{name:\"getTouchStart\",params:{type:\"touchStart\",event:e.changedTouches[0],id:cid, opts: cfu.instance[cid].opts}})\r\n    },\r\n    touchMove(e) {\r\n      let cid = this.rid\r\n      let ontouch = cfu.option[cid].ontouch\r\n      if(ontouch == false) return;\r\n      if(cfu.option[cid].enableScroll === true && e.changedTouches.length == 1){\r\n        cfu.instance[cid].scroll(e);\r\n      }\r\n      if(cfu.option[cid].ontap === true && cfu.option[cid].enableScroll === false && cfu.option[cid].onmovetip === true){\r\n        let rchartdom = document.getElementById('UC'+cid).getBoundingClientRect()\r\n        let tmpe = { x: e.changedTouches[0].clientX - rchartdom.left, y:e.changedTouches[0].clientY - rchartdom.top + rootdom.top}\r\n        e.changedTouches.unshift(tmpe)\r\n        if(cfu.option[cid].tooltipShow === true){\r\n          this.showTooltip(e,cid)\r\n        }\r\n      }\r\n      if(ontouch === true && cfu.option[cid].enableScroll === true && cfu.option[cid].onzoom === true && e.changedTouches.length == 2){\r\n        cfu.instance[cid].dobuleZoom(e);\r\n      }\r\n      that[cid].callMethod('emitMsg',{name:\"getTouchMove\",params:{type:\"touchMove\",event:e.changedTouches[0],id:cid, opts: cfu.instance[cid].opts}})\r\n    },\r\n    touchEnd(e) {\r\n      let cid = this.rid\r\n      let ontouch = cfu.option[cid].ontouch\r\n      if(ontouch == false) return;\r\n      if(cfu.option[cid].enableScroll === true && e.touches.length == 0){\r\n        cfu.instance[cid].scrollEnd(e);\r\n      }\r\n      that[cid].callMethod('emitMsg',{name:\"getTouchEnd\",params:{type:\"touchEnd\",event:e.changedTouches[0],id:cid, opts: cfu.instance[cid].opts}})\r\n    },\r\n    mouseDown(e) {\r\n      let cid = this.rid\r\n      let onmouse = cfu.option[cid].onmouse\r\n      if(onmouse == false) return;\r\n      let rchartdom = document.getElementById('UC'+cid).getBoundingClientRect()\r\n      let tmpe = {}\r\n      tmpe = { x: e.clientX - rchartdom.left, y:e.clientY - rchartdom.top + rootdom.top}\r\n      e.changedTouches = [];\r\n      e.changedTouches.unshift(tmpe)\r\n      cfu.instance[cid].scrollStart(e)\r\n      cfu.option[cid].mousedown=true;\r\n      that[cid].callMethod('emitMsg',{name:\"getTouchStart\",params:{type:\"mouseDown\",event:tmpe,id:cid, opts: cfu.instance[cid].opts}})\r\n    },\r\n    mouseMove(e) {\r\n      let cid = this.rid\r\n      let onmouse = cfu.option[cid].onmouse\r\n      let tooltipShow = cfu.option[cid].tooltipShow\r\n      if(onmouse == false) return;\r\n      let rchartdom = document.getElementById('UC'+cid).getBoundingClientRect()\r\n      let tmpe = {}\r\n      tmpe = { x: e.clientX - rchartdom.left, y:e.clientY - rchartdom.top + rootdom.top}\r\n      e.changedTouches = [];\r\n      e.changedTouches.unshift(tmpe)\r\n      if(cfu.option[cid].mousedown){\r\n        cfu.instance[cid].scroll(e)\r\n        that[cid].callMethod('emitMsg',{name:\"getTouchMove\",params:{type:\"mouseMove\",event:tmpe,id:cid, opts: cfu.instance[cid].opts}})\r\n      }else if(cfu.instance[cid]){\r\n        if(tooltipShow==true){\r\n          this.showTooltip(e,cid)\r\n        }\r\n      }\r\n    },\r\n    mouseUp(e) {\r\n      let cid = this.rid\r\n      let onmouse = cfu.option[cid].onmouse\r\n      if(onmouse == false) return;\r\n      let rchartdom = document.getElementById('UC'+cid).getBoundingClientRect()\r\n      let tmpe = {}\r\n      tmpe = { x: e.clientX - rchartdom.left, y:e.clientY - rchartdom.top + rootdom.top}\r\n      e.changedTouches = [];\r\n      e.changedTouches.unshift(tmpe)\r\n      cfu.instance[cid].scrollEnd(e)\r\n      cfu.option[cid].mousedown=false;\r\n      that[cid].callMethod('emitMsg',{name:\"getTouchEnd\",params:{type:\"mouseUp\",event:tmpe,id:cid, opts: cfu.instance[cid].opts}})\r\n    },\r\n  }\r\n}\r\n</script>\r\n<!-- #endif -->\r\n\r\n<style scoped>\r\n.chartsview {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  flex: 1;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qiun-data-charts.vue?vue&type=style&index=0&id=fe947b98&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qiun-data-charts.vue?vue&type=style&index=0&id=fe947b98&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751115964889\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}