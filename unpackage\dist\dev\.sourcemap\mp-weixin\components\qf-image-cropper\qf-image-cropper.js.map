{"version": 3, "sources": ["webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/components/qf-image-cropper/qf-image-cropper.vue?cc17", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/components/qf-image-cropper/qf-image-cropper.vue?f766", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/components/qf-image-cropper/qf-image-cropper.vue?3f07", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/components/qf-image-cropper/qf-image-cropper.vue?f9ab", "uni-app:///components/qf-image-cropper/qf-image-cropper.vue", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/components/qf-image-cropper/qf-image-cropper.vue?26fc", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/components/qf-image-cropper/qf-image-cropper.vue?1ab1", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/components/qf-image-cropper/qf-image-cropper.wxs?db46", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/components/qf-image-cropper/qf-image-cropper.wxs?899d"], "names": ["name", "options", "styleIsolation", "props", "src", "type", "default", "width", "height", "showBorder", "showGrid", "showAngle", "areaScale", "minScale", "maxScale", "checkRange", "backgroundColor", "bounce", "rotatable", "reverseRotatable", "choosable", "gpu", "angleSize", "angleBorder<PERSON>idth", "zIndex", "radius", "fileType", "delay", "emits", "data", "maskList", "id", "gridList", "angleList", "imgSrc", "imgWidth", "imgHeight", "widthPercent", "heightPercent", "area", "oldWidth", "oldHeight", "sys", "scaleWidth", "scaleHeight", "rotate", "offsetX", "offsetY", "use2d", "canvans<PERSON>id<PERSON>", "canvansHeight", "computed", "initData", "timestamp", "img", "imgProps", "watch", "handler", "scalc", "immediate", "methods", "dataChange", "initArea", "hp", "wp", "left", "right", "top", "bottom", "chooseImage", "uni", "count", "mediaType", "success", "resetData", "initImage", "<PERSON><PERSON><PERSON><PERSON>", "scale", "fail", "console", "drawClipImage", "ctx", "drawImage", "drawRotateImage", "image", "draw2DImage", "callback", "setTimeout", "canvasToTempFilePath", "canvas", "canvasId", "x", "y", "destWidth", "destHeight", "title", "icon", "cropClick", "mask", "query", "fields", "node", "size", "exec", "handleImage", "tempFile<PERSON>ath"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyV;AACzV;AACoE;AACL;AACsC;;;AAGrG;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uTAAM;AACR,EAAE,gUAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2TAAU;AACZ;AACA;;AAEA;AACsO;AACtO,WAAW,wPAAM,iBAAiB,gQAAM;;AAExC;AACe,gF;;;;;;;;;;;;AC3Bf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6mB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmEjoB;AACA;AACA;AACA;AAAA,eAEA;EACAA;EAEAC;IACA;IACAC;EACA;EAEAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;IACA;IACA;IACAY;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACAkB;MACAnB;IACA;IACA;IACAoB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;IACA;IACA;AACA;AACA;AACA;AACA;IACAqB;MACAtB;MACAC;IACA;EAYA;EACAsB;EACAC;IACA;MACA;MACAC,WACA;QAAAC;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,EACA;MACAC,WACA;QAAAD;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,EACA;MACAE,YACA;QAAAF;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,EACA;MACA;MACAG;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;;EACAC;IACAC;MACA;MACA;QACAC;QACAd,sCACA;UACAtB;UACAR;UACAC;UACAC;UACAW;UACAC;UACAV;UACAwB;UACAC;UACAb;UACAV;UACAS;QAAA,EACA;QACAkB;QACAY;UACAzC;UACAC;UACAV;UACAG;UACAC;UACAgC;UACAC;UACApB;QACA;MACA;IACA;IACAkC;MACA;QACAhD;QACAC;QACAJ;MACA;IACA;EACA;EACAoD;IACAD;MACAE;QACA;QACA;QACA;QACA;;QAIA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACAC;QACA;QACA;QACA;QACA;QACA;QACA;QACAtD;MACA;MACAuD;IACA;EACA;EACAC;IACA,0BACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA,kBACAC;MACA;MACA;MAEA;MACA;MAOA;MACA;MACA;QACAC;MACA;QACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QAAAzD;QAAAC;QAAAyD;QAAAC;QAAAC;QAAAC;MAAA;MACA;MACA;IACA;IACA,cACAC;MAAA;MAEA;QACAC,gDACArE;UACAsE;UACAC;UACAC;YACA;YACA;UACA;QAAA,GACA;QACA;MACA;MAEAH,gDACArE;QACAsE;QACAE;UACA;UACA;QACA;MAAA,GACA;IACA;IACA,WACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACAL;QACAlE;QACAqE;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,MACAG;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA;sBAAA;oBAAA;kBAAA;oBACA;oBACAC;oBACAjE;oBACA;sBAAA;sBACA;wBAAA;wBACA;sBACA;wBAAA;wBACA;sBACA;oBACA;sBAAA;sBACA;wBAAA;wBACA;sBACA;wBAAA;wBACA;sBACA;oBACA;oBACA;oBACA;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;UAAA;QAAA;QACAkE;UACAC;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAC;MACA;QACAC;QACAA;QACA;QACA;QACA;UAAA;UACAA;QACA;UAAA;UACA;YAAA;YACAxD;YACA;UACA;;UACAwD;UACAA;UACAA;UACAA;UACAA;UACAA;QACA;QACAA;QACAC;QACAD;MACA;QACAC;MACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;IACAC;MACA;QACA;QACA;QACA;QACAF;QACA;QACAA;QACA;QACAA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA,kEACAD;QACAA;MACA;MACA;QACA;QACA;QACAA,cACAG,OACA,CACA,mCACA,kCACA,sCACA,oCACA,aACA,CACA,kCACA,sCACA,qCACA,kCACA,aACA,2BACA,2BACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACA;QACAD;UACA;UACA;UACAE;QACA;QACAF;UACAL;UACAT;QACA;QACAc;MACA;QACA;QACAG;UACAN;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAO;MAAA;MACA;MACAlB;QACAmB;QACAC;QACAC;QACAC;QACArF;QACAC;QACAqF;QAAA;QACAC;QAAA;QACApE;QAAA;QACA+C;UACA;UACA;QACA;QACAK;UACAR;UACAA;YAAAyB;YAAAC;UAAA;QACA;MACA;IACA;IACA,WACAC;MAAA;MACA3B;QAAAyB;QAAAG;MAAA;MACA;QACA;QACAjB;QACA;UACA;QACA;QACA;MACA;MAEA;MACAkB,2BACAC;QAAAC;QAAAC;MAAA,GACAC;QACA;QAEA;QACAd;QACAA;QACA;QACAR;QACAA;QAEA;UACA;QACA;MACA;IAEA;IACAuB;MACA;MACA;MACAlC;MACA;QAAAmC;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvmBA;AAAA;AAAA;AAAA;AAAgrC,CAAgB,ioCAAG,EAAC,C;;;;;;;;;;;ACApsC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA,wCAAqW,CAAgB,yaAAG,EAAC,C;;;;;;;;;;;;ACAzX;AAAe;AACf;AACA;AACA;AACA;AACA,M", "file": "components/qf-image-cropper/qf-image-cropper.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./qf-image-cropper.vue?vue&type=template&id=40f35364&scoped=true&filter-modules=eyJjcm9wcGVyIjp7InR5cGUiOiJzY3JpcHQiLCJjb250ZW50IjoiIiwic3RhcnQiOjI5NjcsImF0dHJzIjp7Im1vZHVsZSI6ImNyb3BwZXIiLCJsYW5nIjoid3hzIiwic3JjIjoiLi9xZi1pbWFnZS1jcm9wcGVyLnd4cyJ9LCJlbmQiOjI5Njd9fQ%3D%3D&\"\nvar renderjs\nimport script from \"./qf-image-cropper.vue?vue&type=script&lang=js&\"\nexport * from \"./qf-image-cropper.vue?vue&type=script&lang=js&\"\nimport style0 from \"./qf-image-cropper.vue?vue&type=style&index=0&id=40f35364&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"40f35364\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\n/* custom blocks */\nimport block0 from \"./qf-image-cropper.wxs?vue&type=custom&index=0&blockType=script&issuerPath=D%3A%5CHBuilderProjects%5Cspringboot-dubbo-front-wechat%5Ccomponents%5Cqf-image-cropper%5Cqf-image-cropper.vue&module=cropper&lang=wxs\"\nif (typeof block0 === 'function') block0(component)\n\ncomponent.options.__file = \"components/qf-image-cropper/qf-image-cropper.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qf-image-cropper.vue?vue&type=template&id=40f35364&scoped=true&filter-modules=eyJjcm9wcGVyIjp7InR5cGUiOiJzY3JpcHQiLCJjb250ZW50IjoiIiwic3RhcnQiOjI5NjcsImF0dHJzIjp7Im1vZHVsZSI6ImNyb3BwZXIiLCJsYW5nIjoid3hzIiwic3JjIjoiLi9xZi1pbWFnZS1jcm9wcGVyLnd4cyJ9LCJlbmQiOjI5Njd9fQ%3D%3D&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qf-image-cropper.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qf-image-cropper.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"image-cropper\" :style=\"{ zIndex }\" @wheel=\"cropper.mousewheel\">\r\n\t\t<canvas v-if=\"use2d\" type=\"2d\" id=\"imgCanvas\" class=\"img-canvas\" :style=\"{\r\n\t\t\twidth: `${canvansWidth}px`,\r\n\t\t\theight: `${canvansHeight}px`\r\n\t\t}\"></canvas>\r\n\t\t<canvas v-else id=\"imgCanvas\" canvas-id=\"imgCanvas\" class=\"img-canvas\" :style=\"{\r\n\t\t\twidth: `${canvansWidth}px`,\r\n\t\t\theight: `${canvansHeight}px`\r\n\t\t}\"></canvas>\r\n\t\t<view id=\"pic-preview\" class=\"pic-preview\" :change:init=\"cropper.initObserver\" :init=\"initData\" @touchstart=\"cropper.touchstart\" @touchmove=\"cropper.touchmove\" @touchend=\"cropper.touchend\">\r\n\t\t\t<image v-if=\"imgSrc\" id=\"crop-image\" class=\"crop-image\" :style=\"cropper.imageStyles\" :src=\"imgSrc\" webp></image>\r\n\t\t\t<view v-for=\"(item, index) in maskList\" :key=\"item.id\" :id=\"item.id\" class=\"crop-mask-block\" :style=\"cropper.maskStylesList[index]\"></view>\r\n\t\t\t<view v-if=\"showBorder\" id=\"crop-border\" class=\"crop-border\" :style=\"cropper.borderStyles\"></view>\r\n\t\t\t<view v-if=\"radius > 0\" id=\"crop-circle-box\" class=\"crop-circle-box\" :style=\"cropper.circleBoxStyles\">\r\n\t\t\t\t<view class=\"crop-circle\" id=\"crop-circle\" :style=\"cropper.circleStyles\"></view>\r\n\t\t\t</view>\r\n\t\t\t<block v-if=\"showGrid\">\r\n\t\t\t\t<view v-for=\"(item, index) in gridList\" :key=\"item.id\" :id=\"item.id\" class=\"crop-grid\" :style=\"cropper.gridStylesList[index]\"></view>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"showAngle\">\r\n\t\t\t\t<view v-for=\"(item, index) in angleList\" :key=\"item.id\" :id=\"item.id\" class=\"crop-angle\" :style=\"cropper.angleStylesList[index]\">\r\n\t\t\t\t\t<view :style=\"[{\r\n\t\t\t\t\t\twidth: `${angleSize}px`,\r\n\t\t\t\t\t\theight: `${angleSize}px`\r\n\t\t\t\t\t}]\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t<slot />\r\n\t\t<view class=\"fixed-bottom safe-area-inset-bottom\" :style=\"{ zIndex: initData.area.zIndex + 99 }\">\r\n\t\t\t<view v-if=\"(rotatable || reverseRotatable) && !!imgSrc\" class=\"action-bar\">\r\n\t\t\t\t<view v-if=\"reverseRotatable\" class=\"rotate-icon\" @click=\"cropper.rotateImage270\"></view>\r\n\t\t\t\t<view v-if=\"rotatable\" class=\"rotate-icon is-reverse\" @click=\"cropper.rotateImage90\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"!choosable\" class=\"choose-btn\" @click=\"cropClick\">确定</view>\r\n\t\t\t<block v-else-if=\"!!imgSrc\">\r\n\t\t\t\t<view class=\"rechoose\" @click=\"chooseImage\">重选</view>\r\n\t\t\t\t<button class=\"button\" size=\"mini\" @click=\"cropClick\">确定</button>\r\n\t\t\t</block>\r\n\t\t\t<view v-else class=\"choose-btn\" @click=\"chooseImage\">选择图片</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<!-- #ifdef APP-VUE -->\r\n<script module=\"cropper\" lang=\"renderjs\">\r\n\timport cropper from './qf-image-cropper.render.js';\r\n\t// vue3 app renderjs中条件编译无效\r\n\tcropper.setPlatform('APP');\r\n\texport default {\r\n\t\tmixins: [ cropper ]\r\n\t}\r\n</script>\r\n<!-- #endif -->\r\n<!-- #ifdef H5 -->\r\n<script module=\"cropper\" lang=\"renderjs\">\r\n\timport cropper from './qf-image-cropper.render.js';\r\n\texport default {\r\n\t\tmixins: [ cropper ]\r\n\t}\r\n</script>\r\n<!-- #endif -->\r\n<!-- #ifdef MP-WEIXIN || MP-QQ -->\r\n<script module=\"cropper\" lang=\"wxs\" src=\"./qf-image-cropper.wxs\"></script>\r\n<!-- #endif -->\r\n<script>\r\n\t/** 裁剪区域最大宽高所占屏幕宽度百分比 */\r\n\tconst AREA_SIZE = 75;\r\n\t/** 图片默认宽高 */\r\n\tconst IMG_SIZE = 300;\r\n \r\n\texport default {\r\n\t\tname:\"qf-image-cropper\",\r\n\t\t// #ifdef MP-WEIXIN\r\n\t\toptions: {\r\n\t\t\t// 表示启用样式隔离，在自定义组件内外，使用 class 指定的样式将不会相互影响\r\n\t\t\tstyleIsolation: \"isolated\"\r\n\t\t},\r\n\t\t// #endif\r\n\t\tprops: {\r\n\t\t\t/** 图片资源地址 */\r\n\t\t\tsrc: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/** 裁剪宽度，有些平台或设备对于canvas的尺寸有限制，过大可能会导致无法正常绘制 */\r\n\t\t\twidth: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: IMG_SIZE\r\n\t\t\t},\r\n\t\t\t/** 裁剪高度，有些平台或设备对于canvas的尺寸有限制，过大可能会导致无法正常绘制 */\r\n\t\t\theight: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: IMG_SIZE\r\n\t\t\t},\r\n\t\t\t/** 是否绘制裁剪区域边框 */\r\n\t\t\tshowBorder: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t/** 是否绘制裁剪区域网格参考线 */\r\n\t\t\tshowGrid: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t/** 是否展示四个支持伸缩的角 */\r\n\t\t\tshowAngle: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t/** 裁剪区域最小缩放倍数 */\r\n\t\t\tareaScale: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0.3\r\n\t\t\t},\r\n\t\t\t/** 图片最小缩放倍数 */\r\n\t\t\tminScale: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 1\r\n\t\t\t},\r\n\t\t\t/** 图片最大缩放倍数 */\r\n\t\t\tmaxScale: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 5\r\n\t\t\t},\r\n\t\t\t/** 检查图片位置是否超出裁剪边界，如果超出则会矫正位置 */\r\n\t\t\tcheckRange: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t/** 生成图片背景色：如果裁剪区域没有完全包含在图片中时，不设置该属性生成图片存在一定的透明块 */\r\n\t\t\tbackgroundColor: {\r\n\t\t\t\ttype: String\r\n\t\t\t},\r\n\t\t\t/** 是否有回弹效果：当 checkRange 为 true 时有效，拖动时可以拖出边界，释放时会弹回边界 */\r\n\t\t\tbounce: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t/** 是否支持翻转 */\r\n\t\t\trotatable: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t/** 是否支持逆向翻转 */\r\n\t\t\treverseRotatable: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t/** 是否支持从本地选择素材 */\r\n\t\t\tchoosable: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t/** 是否开启硬件加速，图片缩放过程中如果出现元素的“留影”或“重影”效果，可通过该方式解决或减轻这一问题 */\r\n\t\t\tgpu: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t/** 四个角尺寸，单位px */\r\n\t\t\tangleSize: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 20\r\n\t\t\t},\r\n\t\t\t/** 四个角边框宽度，单位px */\r\n\t\t\tangleBorderWidth: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 2\r\n\t\t\t},\r\n\t\t\tzIndex: {\r\n\t\t\t\ttype: [Number, String]\r\n\t\t\t},\r\n\t\t\t/** 裁剪图片圆角半径，单位px */\r\n\t\t\tradius: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\t/** 生成文件的类型，只支持 'jpg' 或 'png'。默认为 'png' */\r\n\t\t\tfileType: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'png'\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 图片从绘制到生成所需时间，单位ms\r\n\t\t\t * 微信小程序平台使用 `Canvas 2D` 绘制时有效\r\n\t\t\t * 如绘制大图或出现裁剪图片空白等情况应适当调大该值，因 `Canvas 2d` 采用同步绘制，需自己把控绘制完成时间\r\n\t\t\t */\r\n\t\t\tdelay: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 1000\r\n\t\t\t},\r\n\t\t\t// #ifdef H5\r\n\t\t\t/** \r\n\t\t\t * 页面是否是原生标题栏\r\n\t\t\t * H5平台当 showAngle 为 true 时，使用插件的页面在 `page.json` 中配置了 \"navigationStyle\": \"custom\" 时，必须将此值设为 false ，否则四个可拉伸角的触发位置会有偏差。\r\n\t\t\t * 注：因H5平台的窗口高度是包含标题栏的，而屏幕触摸点的坐标是不包含的\r\n\t\t\t */\r\n\t\t\tnavigation: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\temits: [\"crop\"],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 用不同 id 使 v-for key 不重复\r\n\t\t\t\tmaskList: [\r\n\t\t\t\t\t{ id: 'crop-mask-block-1' },\r\n\t\t\t\t\t{ id: 'crop-mask-block-2' },\r\n\t\t\t\t\t{ id: 'crop-mask-block-3' },\r\n\t\t\t\t\t{ id: 'crop-mask-block-4' },\r\n\t\t\t\t],\r\n\t\t\t\tgridList: [\r\n\t\t\t\t\t{ id: 'crop-grid-1' },\r\n\t\t\t\t\t{ id: 'crop-grid-2' },\r\n\t\t\t\t\t{ id: 'crop-grid-3' },\r\n\t\t\t\t\t{ id: 'crop-grid-4' },\r\n\t\t\t\t],\r\n\t\t\t\tangleList: [\r\n\t\t\t\t\t{ id: 'crop-angle-1' },\r\n\t\t\t\t\t{ id: 'crop-angle-2' },\r\n\t\t\t\t\t{ id: 'crop-angle-3' },\r\n\t\t\t\t\t{ id: 'crop-angle-4' },\r\n\t\t\t\t],\r\n\t\t\t\t/** 本地缓存的图片路径 */\r\n\t\t\t\timgSrc: '',\r\n\t\t\t\t/** 图片的裁剪宽度 */\r\n\t\t\t\timgWidth: IMG_SIZE,\r\n\t\t\t\t/** 图片的裁剪高度 */\r\n\t\t\t\timgHeight: IMG_SIZE,\r\n\t\t\t\t/** 裁剪区域最大宽度所占屏幕宽度百分比 */\r\n\t\t\t\twidthPercent: AREA_SIZE,\r\n\t\t\t\t/** 裁剪区域最大高度所占屏幕宽度百分比 */\r\n\t\t\t\theightPercent: AREA_SIZE,\r\n\t\t\t\t/** 裁剪区域布局信息 */\r\n\t\t\t\tarea: {},\r\n\t\t\t\t/** 未被缩放过的图片宽 */\r\n\t\t\t\toldWidth: 0,\r\n\t\t\t\t/** 未被缩放过的图片高 */\r\n\t\t\t\toldHeight: 0,\r\n\t\t\t\t/** 系统信息 */\r\n\t\t\t\tsys: uni.getSystemInfoSync(),\r\n\t\t\t\tscaleWidth: 0,\r\n\t\t\t\tscaleHeight: 0,\r\n\t\t\t\trotate: 0,\r\n\t\t\t\toffsetX: 0,\r\n\t\t\t\toffsetY: 0,\r\n\t\t\t\tuse2d: false,\r\n\t\t\t\tcanvansWidth: 0,\r\n\t\t\t\tcanvansHeight: 0,\r\n\t\t\t\t// imageStyles: {},\r\n\t\t\t\t// maskStylesList: [{}, {}, {}, {}],\r\n\t\t\t\t// borderStyles: {},\r\n\t\t\t\t// gridStylesList: [{}, {}, {}, {}],\r\n\t\t\t\t// angleStylesList: [{}, {}, {}, {}],\r\n\t\t\t\t// circleBoxStyles: {},\r\n\t\t\t\t// circleStyles: {},\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tinitData() {\r\n\t\t\t\t// console.log('initData')\r\n\t\t\t\treturn {\r\n\t\t\t\t\ttimestamp: new Date().getTime(),\r\n\t\t\t\t\tarea: {\r\n\t\t\t\t\t\t...this.area,\r\n\t\t\t\t\t\tbounce: this.bounce,\r\n\t\t\t\t\t\tshowBorder: this.showBorder,\r\n\t\t\t\t\t\tshowGrid: this.showGrid,\r\n\t\t\t\t\t\tshowAngle: this.showAngle,\r\n\t\t\t\t\t\tangleSize: this.angleSize,\r\n\t\t\t\t\t\tangleBorderWidth: this.angleBorderWidth,\r\n\t\t\t\t\t\tminScale: this.areaScale,\r\n\t\t\t\t\t\twidthPercent: this.widthPercent,\r\n\t\t\t\t\t\theightPercent: this.heightPercent,\r\n\t\t\t\t\t\tradius: this.radius,\r\n\t\t\t\t\t\tcheckRange: this.checkRange,\r\n\t\t\t\t\t\tzIndex: +this.zIndex || 0,\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsys: this.sys,\r\n\t\t\t\t\timg: {\r\n\t\t\t\t\t\tminScale: this.minScale,\r\n\t\t\t\t\t\tmaxScale: this.maxScale,\r\n\t\t\t\t\t\tsrc: this.imgSrc,\r\n\t\t\t\t\t\twidth: this.oldWidth,\r\n\t\t\t\t\t\theight: this.oldHeight,\r\n\t\t\t\t\t\toldWidth: this.oldWidth,\r\n\t\t\t\t\t\toldHeight: this.oldHeight,\r\n\t\t\t\t\t\tgpu: this.gpu,\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timgProps() {\r\n\t\t\t\treturn {\r\n\t\t\t\t\twidth: this.width,\r\n\t\t\t\t\theight: this.height,\r\n\t\t\t\t\tsrc: this.src,\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\timgProps: {\r\n\t\t\t\thandler(val, oldVal) {\r\n\t\t\t\t\t// 自定义裁剪尺，示例如下：\r\n\t\t\t\t\tthis.imgWidth = Number(val.width) || IMG_SIZE;\r\n\t\t\t\t\tthis.imgHeight = Number(val.height) || IMG_SIZE;\r\n\t\t\t\t\tlet use2d = true;\r\n\t\t\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\t\t\tuse2d = false;\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// if(use2d && (this.imgWidth > 1365 || this.imgHeight > 1365)) {\r\n\t\t\t\t\t// \tuse2d = false;\r\n\t\t\t\t\t// }\r\n\t\t\t\t\tlet canvansWidth = this.imgWidth;\r\n\t\t\t\t\tlet canvansHeight = this.imgHeight;\r\n\t\t\t\t\tlet size = Math.max(canvansWidth, canvansHeight)\r\n\t\t\t\t\tlet scalc = 1;\r\n\t\t\t\t\tif(size > 1365) {\r\n\t\t\t\t\t\tscalc = 1365 / size;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.canvansWidth = canvansWidth * scalc;\r\n\t\t\t\t\tthis.canvansHeight = canvansHeight * scalc;\r\n\t\t\t\t\tthis.use2d = use2d;\r\n\t\t\t\t\tthis.initArea();\r\n\t\t\t\t\tconst src = val.src || this.imgSrc;\r\n\t\t\t\t\tsrc && this.initImage(src, oldVal === undefined);\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t/** 提供给wxs调用，用来接收图片变更数据 */\r\n\t\t\tdataChange(e) {\r\n\t\t\t\t// console.log('dataChange', e)\r\n\t\t\t\tthis.scaleWidth = e.width;\r\n\t\t\t\tthis.scaleHeight = e.height;\r\n\t\t\t\tthis.rotate = e.rotate;\r\n\t\t\t\tthis.offsetX = e.x;\r\n\t\t\t\tthis.offsetY = e.y;\r\n\t\t\t},\r\n\t\t\t/** 初始化裁剪区域布局信息 */\r\n\t\t\tinitArea() {\r\n\t\t\t\t// 底部操作栏高度 = 底部底部操作栏内容高度 + 设备底部安全区域高度\r\n\t\t\t\tthis.sys.offsetBottom = uni.upx2px(100) + this.sys.safeAreaInsets.bottom;\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\tthis.sys.windowTop = 0;\r\n\t\t\t\tthis.sys.navigation = true;\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\t// h5平台的窗口高度是包含标题栏的\r\n\t\t\t\tthis.sys.windowTop = this.sys.windowTop || 44;\r\n\t\t\t\tthis.sys.navigation = this.navigation;\r\n\t\t\t\t// #endif\r\n\t\t\t\tlet wp = this.widthPercent;\r\n\t\t\t\tlet hp = this.heightPercent;\r\n\t\t\t\tif (this.imgWidth > this.imgHeight) {\r\n\t\t\t\t\thp = hp * this.imgHeight / this.imgWidth;\r\n\t\t\t\t} else if (this.imgWidth < this.imgHeight) {\r\n\t\t\t\t\twp = wp * this.imgWidth / this.imgHeight;\r\n\t\t\t\t}\r\n\t\t\t\tconst size = this.sys.windowWidth > this.sys.windowHeight ? this.sys.windowHeight : this.sys.windowWidth;\r\n\t\t\t\tconst width = size * wp / 100;\r\n\t\t\t\tconst height = size * hp / 100;\r\n\t\t\t\tconst left = (this.sys.windowWidth - width) / 2;\r\n\t\t\t\tconst right = left + width;\r\n\t\t\t\tconst top = (this.sys.windowHeight + this.sys.windowTop - this.sys.offsetBottom - height) / 2;\r\n\t\t\t\tconst bottom = this.sys.windowHeight + this.sys.windowTop - this.sys.offsetBottom - top;\r\n\t\t\t\tthis.area = { width, height, left, right, top, bottom };\r\n\t\t\t\tthis.scaleWidth = width;\r\n\t\t\t\tthis.scaleHeight = height;\r\n\t\t\t},\r\n\t\t\t/** 从本地选取图片 */\r\n\t\t\tchooseImage(options) {\r\n\t\t\t\t// #ifdef MP-WEIXIN || MP-JD\r\n\t\t\t\tif(uni.chooseMedia) {\r\n\t\t\t\t\tuni.chooseMedia({\r\n\t\t\t\t\t\t...options,\r\n\t\t\t\t\t\tcount: 1,\r\n\t\t\t\t\t\tmediaType: ['image'],\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tthis.resetData();\r\n\t\t\t\t\t\t\tthis.initImage(res.tempFiles[0].tempFilePath);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tuni.chooseImage({\r\n\t\t\t\t\t...options,\r\n\t\t\t\t\tcount: 1,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tthis.resetData();\r\n\t\t\t\t\t\tthis.initImage(res.tempFiles[0].path);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/** 重置数据 */\r\n\t\t\tresetData() {\r\n\t\t\t\tthis.imgSrc = '';\r\n\t\t\t\tthis.rotate = 0;\r\n\t\t\t\tthis.offsetX = 0;\r\n\t\t\t\tthis.offsetY = 0;\r\n\t\t\t\tthis.initArea();\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 初始化图片信息\r\n\t\t\t * @param {String} url 图片链接\r\n\t\t\t */\r\n\t\t\tinitImage(url, isFirst) {\r\n\t\t\t\tuni.getImageInfo({\r\n\t\t\t\t\tsrc: url,\r\n\t\t\t\t\tsuccess: async (res) => {\r\n\t\t\t\t\t\tif (isFirst && this.src === url) await (new Promise((resolve) => setTimeout(resolve, 50)));\r\n\t\t\t\t\t\tthis.imgSrc = res.path;\r\n\t\t\t\t\t\tlet scale = res.width / res.height;\r\n\t\t\t\t\t\tlet areaScale = this.area.width / this.area.height;\r\n\t\t\t\t\t\tif (scale > 1) { // 横向图片\r\n\t\t\t\t\t\t\tif (scale >= areaScale) { // 图片宽不小于目标宽，则高固定，宽自适应\r\n\t\t\t\t\t\t\t\tthis.scaleWidth = (this.scaleHeight / res.height) * this.scaleWidth * (res.width / this.scaleWidth);\r\n\t\t\t\t\t\t\t} else { // 否则宽固定、高自适应\r\n\t\t\t\t\t\t\t\tthis.scaleHeight = res.height * this.scaleWidth / res.width;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else { // 纵向图片\r\n\t\t\t\t\t\t\tif (scale <= areaScale) { // 图片高不小于目标高，宽固定，高自适应\r\n\t\t\t\t\t\t\t\tthis.scaleHeight = (this.scaleWidth / res.width) * this.scaleHeight / (this.scaleHeight / res.height);\r\n\t\t\t\t\t\t\t} else { // 否则高固定，宽自适应\r\n\t\t\t\t\t\t\t\tthis.scaleWidth = res.width * this.scaleHeight / res.height;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 记录原始宽高，为缩放比列做限制\r\n\t\t\t\t\t\tthis.oldWidth = +this.scaleWidth.toFixed(2);\r\n\t\t\t\t\t\tthis.oldHeight = +this.scaleHeight.toFixed(2);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error(err)\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 剪切图片圆角\r\n\t\t\t * @param {Object} ctx canvas 的绘图上下文对象\r\n\t\t\t * @param {Number} radius 圆角半径\r\n\t\t\t * @param {Number} scale 生成图片的实际尺寸与截取区域比\r\n\t\t\t * @param {Function} drawImage 执行剪切时所调用的绘图方法，入参为是否执行了剪切\r\n\t\t\t */\r\n\t\t\tdrawClipImage(ctx, radius, scale, drawImage) {\r\n\t\t\t\tif(radius > 0) {\r\n\t\t\t\t\tctx.save();\r\n\t\t\t\t\tctx.beginPath();\r\n\t\t\t\t\tconst w = this.canvansWidth;\r\n\t\t\t\t\tconst h = this.canvansHeight;\r\n\t\t\t\t\tif(w === h && radius >= w / 2) { // 圆形\r\n\t\t\t\t\t\tctx.arc(w / 2, h / 2, w / 2, 0, 2 * Math.PI);\r\n\t\t\t\t\t} else { // 圆角矩形\r\n\t\t\t\t\t\tif(w !== h) { // 限制圆角半径不能超过短边的一半\r\n\t\t\t\t\t\t\tradius = Math.min(w / 2, h / 2, radius);\r\n\t\t\t\t\t\t\t// radius = Math.min(Math.max(w, h) / 2, radius);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tctx.moveTo(radius, 0);\r\n\t\t\t\t\t\tctx.arcTo(w, 0, w, h, radius);\r\n\t\t\t\t\t\tctx.arcTo(w, h, 0, h, radius);\r\n\t\t\t\t\t\tctx.arcTo(0, h, 0, 0, radius);\r\n\t\t\t\t\t\tctx.arcTo(0, 0, w, 0, radius);\r\n\t\t\t\t\t\tctx.closePath();\r\n\t\t\t\t\t}\r\n\t\t\t\t\tctx.clip();\r\n\t\t\t\t\tdrawImage && drawImage(true);\r\n\t\t\t\t\tctx.restore();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tdrawImage && drawImage(false);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 旋转图片\r\n\t\t\t * @param {Object} ctx canvas 的绘图上下文对象\r\n\t\t\t * @param {Number} rotate 旋转角度\r\n\t\t\t * @param {Number} scale 生成图片的实际尺寸与截取区域比\r\n\t\t\t */\r\n\t\t\tdrawRotateImage(ctx, rotate, scale) {\r\n\t\t\t\tif(rotate !== 0) {\r\n\t\t\t\t\t// 1. 以图片中心点为旋转中心点\r\n\t\t\t\t\tconst x = this.scaleWidth * scale / 2;\r\n\t\t\t\t\tconst y = this.scaleHeight * scale / 2;\r\n\t\t\t\t\tctx.translate(x, y);\r\n\t\t\t\t\t// 2. 旋转画布\r\n\t\t\t\t\tctx.rotate(rotate * Math.PI / 180);\r\n\t\t\t\t\t// 3. 旋转完画布后恢复设置旋转中心时所做的偏移\r\n\t\t\t\t\tctx.translate(-x, -y);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tdrawImage(ctx, image, callback) {\r\n\t\t\t\t// 生成图片的实际尺寸与截取区域比\r\n\t\t\t\tconst scale = this.canvansWidth / this.area.width;\r\n\t\t\t\tif(this.backgroundColor) {\r\n\t\t\t\t\tif(ctx.setFillStyle) ctx.setFillStyle(this.backgroundColor);\r\n\t\t\t\t\telse ctx.fillStyle = this.backgroundColor;\r\n\t\t\t\t\tctx.fillRect(0, 0, this.canvansWidth, this.canvansHeight);\r\n\t\t\t\t}\r\n\t\t\t\tthis.drawClipImage(ctx, this.radius, scale, () => {\r\n\t\t\t\t\tthis.drawRotateImage(ctx, this.rotate, scale);\r\n\t\t\t\t\tconst r = this.rotate / 90;\r\n\t\t\t\t\tctx.drawImage(\r\n\t\t\t\t\t\timage,\r\n\t\t\t\t\t\t[\r\n\t\t\t\t\t\t\t(this.offsetX - this.area.left),\r\n\t\t\t\t\t\t\t(this.offsetY - this.area.top),\r\n\t\t\t\t\t\t\t-(this.offsetX - this.area.left),\r\n\t\t\t\t\t\t\t-(this.offsetY - this.area.top)\r\n\t\t\t\t\t\t][r] * scale,\r\n\t\t\t\t\t\t[\r\n\t\t\t\t\t\t\t(this.offsetY - this.area.top),\r\n\t\t\t\t\t\t\t-(this.offsetX - this.area.left),\r\n\t\t\t\t\t\t\t-(this.offsetY - this.area.top),\r\n\t\t\t\t\t\t\t(this.offsetX - this.area.left)\r\n\t\t\t\t\t\t][r] * scale,\r\n\t\t\t\t\t\tthis.scaleWidth * scale,\r\n\t\t\t\t\t\tthis.scaleHeight * scale\r\n\t\t\t\t\t);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 绘图\r\n\t\t\t * @param {Object} canvas \r\n\t\t\t * @param {Object} ctx canvas 的绘图上下文对象\r\n\t\t\t * @param {String} src 图片路径\r\n\t\t\t * @param {Function} callback 开始绘制时回调\r\n\t\t\t */\r\n\t\t\tdraw2DImage(canvas, ctx, src, callback) {\r\n\t\t\t\t// console.log('draw2DImage', canvas, ctx, src, callback)\r\n\t\t\t\tif(canvas) {\r\n\t\t\t\t\tconst image = canvas.createImage();\r\n\t\t\t\t\timage.onload = () => {\r\n\t\t\t\t\t\tthis.drawImage(ctx, image);\r\n\t\t\t\t\t\t// 如果觉得`生成时间过长`或`出现生成图片空白`可尝试调整延迟时间\r\n\t\t\t\t\t\tcallback && setTimeout(callback, this.delay);\r\n\t\t\t\t\t};\r\n\t\t\t\t\timage.onerror = (err) => {\r\n\t\t\t\t\t\tconsole.error(err)\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t};\r\n\t\t\t\t\timage.src = src;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.drawImage(ctx, src);\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tctx.draw(false, callback);\r\n\t\t\t\t\t}, 200);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 画布转图片到本地缓存\r\n\t\t\t * @param {Object} canvas \r\n\t\t\t * @param {String} canvasId \r\n\t\t\t */\r\n\t\t\tcanvasToTempFilePath(canvas, canvasId) {\r\n\t\t\t\t// console.log('canvasToTempFilePath', canvas, canvasId)\r\n\t\t\t\tuni.canvasToTempFilePath({\r\n\t\t\t\t\tcanvas,\r\n\t\t\t\t\tcanvasId,\r\n\t\t\t\t\tx: 0,\r\n\t\t\t\t\ty: 0,\r\n\t\t\t\t\twidth: this.canvansWidth,\r\n\t\t\t\t\theight: this.canvansHeight,\r\n\t\t\t\t\tdestWidth: this.imgWidth, // 必要，保证生成图片宽度不受设备分辨率影响\r\n\t\t\t\t\tdestHeight: this.imgHeight, // 必要，保证生成图片高度不受设备分辨率影响\r\n\t\t\t\t\tfileType: this.fileType, // 目标文件的类型，默认png\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t// 生成的图片临时文件路径\r\n\t\t\t\t\t\tthis.handleImage(res.tempFilePath);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showToast({ title: '裁剪失败，生成图片异常！', icon: 'none' });\r\n\t\t\t\t\t}\r\n\t\t\t\t}, this);\r\n\t\t\t},\r\n\t\t\t/** 确认裁剪 */\r\n\t\t\tcropClick() {\r\n\t\t\t\tuni.showLoading({ title: '裁剪中...', mask: true });\r\n\t\t\t\tif(!this.use2d) {\r\n\t\t\t\t\tconst ctx = uni.createCanvasContext('imgCanvas', this);\r\n\t\t\t\t\tctx.clearRect(0, 0, this.canvansWidth, this.canvansHeight);\r\n\t\t\t\t\tthis.draw2DImage(null, ctx, this.imgSrc, () => {\r\n\t\t\t\t\t\tthis.canvasToTempFilePath(null, 'imgCanvas');\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\tquery.select('#imgCanvas')\r\n\t\t\t\t\t.fields({ node: true, size: true })\r\n\t\t\t\t\t.exec((res) => {\r\n\t\t\t\t\t\tconst canvas = res[0].node;\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\tconst dpr = uni.getSystemInfoSync().pixelRatio;\r\n\t\t\t\t\t\tcanvas.width = res[0].width * dpr;\r\n\t\t\t\t\t\tcanvas.height = res[0].height * dpr;\r\n\t\t\t\t\t\tconst ctx = canvas.getContext('2d');\r\n\t\t\t\t\t\tctx.scale(dpr, dpr);\r\n\t\t\t\t\t\tctx.clearRect(0, 0, this.canvansWidth, this.canvansHeight);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthis.draw2DImage(canvas, ctx, this.imgSrc, () => {\r\n\t\t\t\t\t\t\tthis.canvasToTempFilePath(canvas);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\thandleImage(tempFilePath){\r\n\t\t\t\t// 在H5平台下，tempFilePath 为 base64\r\n\t\t\t\t// console.log(tempFilePath)\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tthis.$emit('crop', { tempFilePath });\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n \r\n<style lang=\"scss\" scoped>\r\n\t.image-cropper {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\ttop: 0;\r\n\t\tbottom: 0;\r\n\t\toverflow: hidden;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tbackground-color: #000;\r\n\t\t.img-canvas {\r\n\t\t\tposition: absolute !important;\r\n\t\t\ttransform: translateX(-100%);\r\n\t\t}\r\n\t\t.pic-preview {\r\n\t\t\twidth: 100%;\r\n\t\t\tflex: 1;\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t.crop-mask-block {\r\n\t\t\t\tbackground-color: rgba(51, 51, 51, 0.8);\r\n\t\t\t\tz-index: 2;\r\n\t\t\t\tposition: fixed;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tpointer-events: none;\r\n\t\t\t}\r\n\t\t\t.crop-circle-box {\r\n\t\t\t\tposition: fixed;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tz-index: 2;\r\n\t\t\t\tpointer-events: none;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\t.crop-circle {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.crop-image {\r\n\t\t\t\tpadding: 0 !important;\r\n\t\t\t\tmargin: 0 !important;\r\n\t\t\t\tborder-radius: 0 !important;\r\n\t\t\t\tdisplay: block !important;\r\n\t\t\t\tbackface-visibility: hidden;\r\n\t\t\t}\r\n\t\t\t.crop-border {\r\n\t\t\t\tposition: fixed;\r\n\t\t\t\tborder: 1px solid #fff;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tz-index: 3;\r\n\t\t\t\tpointer-events: none;\r\n\t\t\t}\r\n\t\t\t.crop-grid {\r\n\t\t\t\tposition: fixed;\r\n\t\t\t\tz-index: 3;\r\n\t\t\t\tborder-style: dashed;\r\n\t\t\t\tborder-color: #fff;\r\n\t\t\t\tpointer-events: none;\r\n\t\t\t\topacity: 0.5;\r\n\t\t\t}\r\n\t\t\t.crop-angle {\r\n\t\t\t\tposition: fixed;\r\n\t\t\t\tz-index: 3;\r\n\t\t\t\tborder-style: solid;\r\n\t\t\t\tborder-color: #fff;\r\n\t\t\t\tpointer-events: none;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.fixed-bottom {\r\n\t\t\tposition: fixed;\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t\tbottom: 0;\r\n\t\t\tz-index: 99;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: row;\r\n\t\t\tbackground-color: $uni-bg-color-grey;\r\n\r\n\t\t\t.action-bar {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: -90rpx;\r\n\t\t\t\tleft: 10rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\t.rotate-icon {\r\n\t\t\t\t\tbackground-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABCFJREFUaEPtml3IpVMUx3//ko/ChTIyiGFSMyhllI8bc4F85yuNC2FCqLmQC1+FZORiEkUMNW7UjKjJULgxV+NzSkxDhEkZgwsyigv119J63p7zvOc8z37OmXdOb51dz82711r7/99r7bXXXucVi3xokeNnRqCvB20fDmwAlgK/5bcD+FTSr33tHXQP2H4MeHQE0A+B5yRtLiUyDQJrgVc6AAaBpyV93kXkoBMIQLbfBS5NcK8BRwDXNcD+AdwnaVMbiWkRCPBBohpxHuK7M7865sclRdgNHVMhkF6IMIpwirFEUhzo8M7lwIvASTXEqyVtH8ZgagQSbOzsDknv18HZXpHn5IL8+94IOUm7miSmSqAttjPdbgGuTrnNktYsGgLpoYuAD2qg1zRTbG8P2D4SOC6/Q7vSHPALsE/S7wWy80RsPw/ckxMfSTq/LtRJwPbxwF3ASiCUTxwHCPAnEBfVF8AWSTtL7Ng+LfWOTfmlkn6udFsJ5K15R6a4kvX6yGyUFBvTOWzHXXFzCt4g6c1OArYj9iIGh43YgR+BvztXh1PSa4cMkd0jaVmXDduPAE+k3HpJD7cSGFKvfAc8FQUX8IOk/V2L1udtB/hTgdOBW4Aba/M7Ja1qs2f7euCNlHlZUlx4/495IWQ7Jl+qGbxX0gt9AHfJ2o6zFBVoNVrDKe+F3Sm8VdK1bQQ+A85JgXckXdkFaJx527cC9TpnVdvBtl3h2iapuhsGPdBw1b9xnUvaNw7AEh3bnwDnpuwGSfeP0rN9NvAMELXRXFkxEEK2nwQeSiOtRVQJwC4Z29cAW1Nuu6TVXTrN+SaBt4ErUug2Sa/2NdhH3vZy4NvU2S/p6D768w5xI3WOrAD7LtISFpGdIhVXKfaYvjd20wP13L9M0p4DBbaFRKToSLExVkr6qs+aIwlI6iwz+izUQqC+ab29PiMwqRcmPXczD8w8MFj1zg7xXEqbpdHCw7FgWSjafZL+KcQxtpjteCeflwYulFR/J3TabSslVkj6utPChAK2f6q9uZdLitKieLQRuExSvX9ZbLRUMFs09efpUZL+KtUfVo1GW/umNHC3pOhRLtiwfSbwZS6wV9IJfRdreuBBYH0a2STp9r4G+8jbXgc8mzoDT8VSO00ClwDv1ZR7XyylC4ec7ejaLUmdsV6Aw7oSbwFXpdFdks7qA6pU1na0aR6owgeIR/1cx63UzjAC0YXYVjMQHlkn6ZtSo21ytuPZGKFagQ/xsXZ/3iGuFrYdjafXG0DiQMeBi47c9/GV3BO247UV38n5o0UAP6xmu7jFOGxjRr66On5NPBDOCBsDTapxjHY1dyOcolNXnYlx1himE53p2PmNkxosevfavhg4Izt2k7TXPwZ2S6p6QZPin/2rwcQ7OKmBohCadJGF1P8PG6aaQBKVX/8AAAAASUVORK5CYII=');\r\n\t\t\t\t\tbackground-size: 60% 60%;\r\n\t\t\t\t\tbackground-repeat: no-repeat;\r\n\t\t\t\t\tbackground-position: center;\r\n\t\t\t\t\twidth: 80rpx;\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\t&.is-reverse {\r\n\t\t\t\t\t\ttransform: rotateY(180deg);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t \r\n\t\t\t.rechoose {\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\tpadding: 0 $uni-spacing-row-lg;\r\n\t\t\t\tline-height: 100rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.choose-btn {\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tline-height: 100rpx;\r\n\t\t\t\tflex: 1;\r\n\t\t\t}\r\n\r\n\t\t\t.button {\r\n\t\t\t\tmargin: auto $uni-spacing-row-lg auto auto;\r\n\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.safe-area-inset-bottom {\r\n\t\t\tpadding-bottom: 0;  \r\n\t\t\tpadding-bottom: constant(safe-area-inset-bottom); // 兼容 IOS<11.2\r\n\t\t\tpadding-bottom: env(safe-area-inset-bottom); // 兼容 IOS>=11.2\r\n\t\t}\r\n \r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qf-image-cropper.vue?vue&type=style&index=0&id=40f35364&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qf-image-cropper.vue?vue&type=style&index=0&id=40f35364&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751115964946\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!./qf-image-cropper.wxs?vue&type=custom&index=0&blockType=script&issuerPath=D%3A%5CHBuilderProjects%5Cspringboot-dubbo-front-wechat%5Ccomponents%5Cqf-image-cropper%5Cqf-image-cropper.vue&module=cropper&lang=wxs\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!./qf-image-cropper.wxs?vue&type=custom&index=0&blockType=script&issuerPath=D%3A%5CHBuilderProjects%5Cspringboot-dubbo-front-wechat%5Ccomponents%5Cqf-image-cropper%5Cqf-image-cropper.vue&module=cropper&lang=wxs\"", "export default function (Component) {\n       if(!Component.options.wxsCallMethods){\n         Component.options.wxsCallMethods = []\n       }\n       Component.options.wxsCallMethods.push('dataChange')\n     }"], "sourceRoot": ""}